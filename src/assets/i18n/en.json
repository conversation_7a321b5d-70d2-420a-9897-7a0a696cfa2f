{"common": {"mainHeader.app.title": "ONE Record", "mainHeader.app.subtitle": "Living Lab", "mainHeader.mainNav.sli": "Record Management", "mainHeader.mainNav.upload": "Upload", "mainHeader.mainNav.payment": "Payment", "mainHeader.mainNav.fileBrowser": "File Browser", "mainHeader.mainNav.checkin": "Check-in", "mainHeader.mainNav.mf": "MF", "mainHeader.mainNav.users": "Users", "mainHeader.settings": "Settings", "mainHeader.account": "Account", "mainHeader.resources": "Resources", "mainHeader.logout": "Logout", "mainFooter.title": "Our mission is to represent, <br/> lead and serve the airline industry", "mainFooter.support": "Support", "mainFooter.services": "Services", "mainFooter.store": "IATA store", "mainFooter.privacy": "Privacy", "mainFooter.legal": "Legal", "breadcrumb.home": "Home", "dialog.next": "Next", "dialog.cancel": "Cancel", "dialog.ok": "OK", "dialog.cancel.content": "Are you sure to cancel?", "selectOrg.title": "Select One from Below", "dialog.form.validate": "Please fill out all the required and valid data.", "dialog.orglist.fail": "get organization list failed", "dialog.orginfo.fail": "get organization info failed"}, "sli": {"mgmt.title": "Shipper's Letter of Instructions (SLI) Management", "mgmt.goodsDescription": "Goods Description", "mgmt.sliCode": "SLI Code", "mgmt.shipper": "Shipper Company Name", "mgmt.consignee": "Consignee Company Name", "mgmt.departureLocation": "Origin", "mgmt.arrivalLocation": "Destination", "mgmt.hawbNumber": "Associated HAWB", "mgmt.createDate": "Created Date Range", "mgmt.search": "Search", "mgmt.reset": "Reset", "mgmt.create": "Create SLI", "mgmt.cancel": "Cancel", "mgmt.save": "Save", "mgmt.add.piece": "Piece", "mgmt.delete.piece": "Delete", "mgmt.list": "<PERSON><PERSON>'s Letter of Instructions (SLI) List", "mgmt.company": {"companyName": "Company Name", "contactName": "Contact Name", "country": "Country", "province": "Province", "city": "City", "postalCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "shipper": "Shipper", "consignee": "Consignee", "alsoNotify": "Also Notify", "companyName.required": "Company Name is required", "contactName.required": "Contact Name is required", "country.required": "Country is required", "province.required": "Province is required", "city.required": "City is required", "address.required": "Address is required", "phoneNumber.required": "Phone Number is required", "pattern": {"number": "Please enter number", "email": "Please enter a valid email address"}}, "mgmt.routing": {"departureLocation": "Airport of Departure", "arrivalLocation": "Airport of Destination", "departureLocation.required": "Airport of Departure is required", "arrivalLocation.required": "Airport of Destination is required", "shippingInfo": "Requested Routing"}, "mgmt.pieceList": {"goodsDescription": "Description of Goods", "totalGrossWeight": "Total Gross Weight", "totalDimensions": "Total Dimensions", "dimLength": "Length", "dimWidth": "<PERSON><PERSON><PERSON>", "dimHeight": "Height", "goodsDescription.required": "Description of Goods is required", "totalGrossWeight.required": "Total Gross Weight is required", "dimLength.required": "Length is required", "dimWidth.required": "Width is required", "dimHeight.required": "Height is required", "declaredValueForCustoms": "Declared Value for Customs", "declaredValueForCarriage": "Declared Value for Carriage", "insuredAmount": "Insurance Amount Requested", "textualHandlingInstructions": "Handling Information Remarks", "weightValuationIndicator": "Weight Valuation Indicator", "incoterms": "Incoterms", "pattern": {"positiveNumber": "positive number", "decimalNumber1": "positive number with maximum 1 decimal", "decimalNumber2": "positive number with maximum 2 decimals", "decimalNumber2NIL": "positive number with maximum 2 decimals or NIL", "decimalNumber2NVD": "positive number with maximum 2 decimals or NVD", "decimalNumber2NCV": "positive number with maximum 2 decimals or NCV"}}, "piece": {"table.column.productDescription": "Product Description", "table.column.packagingType": "Packaging Type", "table.column.grossWeight": "Gross Weight(KG)", "table.column.dimensions": "Dimensions", "table.column.pieceQuantity": "Piece Quantity", "table.column.slac": "SLAC", "addDialog.title": "Choose Piece Type", "addDialog.subtitle": "Please select piece type of you want to add", "addDialog.general": "General <PERSON>", "addDialog.dg": "Dangerous Goods", "addDialog.la": "Live Animals", "addDialog.pieceType.required": "Piece type is required", "grossWeight.required": "Gross Weight is required", "packagingType.required": "Packaging Type is required", "add": "Add Piece", "title": "Piece", "productDescription": "Product Description", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "upid": "UPID", "packagingType": "Type of Package", "packagedIdentifier": "Packaged Identifier", "hsCommodityDescription": "HS Commodity Description", "nvdForCustoms": "Whether have Declared Value for Customs", "nvdForCarriage": "Whether have Declared Value for Carriage", "shippingMarks": "Shipping Marks", "textualHandlingInstructions": "textual Handling Instructions", "item.title": "Do you want to create Contained Pieces in this Piece?", "item.add": "Add Item", "contained.yes": "Yes", "contained.no": "No", "item.description": "Item Description", "item.weight": "Weight", "item.quantity": "Quantity", "item.total": "Total Items"}, "table.column.sliCode": "SLI Code", "table.column.shipper": "Shipper", "table.column.consignee": "Consignee", "table.column.goodsDescription": "Goods Description", "table.column.departureLocation": "Origin", "table.column.arrivalLocation": "Destination", "table.column.slac": "Piece Quantity", "table.column.createDate": "Created Date", "table.column.hawbNumber": "Associated HAWB", "table.column.share": "Share", "dialog.create.success": "create SLI successfully", "dialog.create.fail": "create SLI failed", "dialog.list.fail": "get SLI list failed", "dialog.shipper.fail": "get shipper info failed"}, "upload": {"browser.dragAndDrop": "Drag & Drop file(s) here", "browser.or": "or", "browser.browse": "Browse for file(s)", "progress.lastModified": "Last modified at:", "progress.fileSize": "File size:"}, "filesManagement": {"category": "File category", "table.column.name": "Name", "table.column.size": "Size", "table.column.createdAt": "Created at", "table.column.actions": "Actions", "table.action.view": "View", "table.action.download": "Download", "table.action.delete": "Delete", "noFilesFound": "Sorry, we didn't find any files. You might need to upload them first!", "selectedFile.header": "Selected file view"}, "ifgPayment": {"paymentInitFailed": "Payment initiation failed!", "renderFailed": "Payment screen rendering failed!"}, "checkin": {"passenger": {"title": "Passenger details", "name": "Full Name", "gender": "Gender", "nationality": "Nationality", "nationality.placeholder": "Select Nationality", "birthDate": "Birth Date"}, "flight": {"title": "Flight details", "carrier": "Operating Carrier", "carrier.placeholder": "Select Carrier", "departureAirport": "Departure Airport", "departureAirport.placeholder": "Select Airport", "departureDate": "Departure Date", "arrivalAirport": "Arrival Airport", "arrivalAirport.placeholder": "Select Airport", "arrivalDate": "Arrival Date", "flightNumber": "Flight Number", "addLeg": "Add Leg", "deleteLeg": "Delete Leg", "checkin": "Check-in"}, "boardingPass": {"title": "Here is your boarding pass!", "subTitle": "Send boarding pass to your e-mail address:", "email": "Email", "send": "Send"}}, "usersManagement": {"table.column.userName": "User Name", "table.column.email": "Email", "table.column.roles": "Roles", "table.column.status": "Status", "table.column.lastAccessed": "Last Accessed At", "table.column.actions": "Actions", "table.action.updateUser": "Update User", "buttons.create": "Add User", "noDataFound": "We didn't find any existing user!", "create.title": "Add User", "create.firstName": "First Name", "create.firstName.required": "First Name is required", "create.lastName": "Last Name", "create.lastName.required": "Last Name is required", "create.email": "Email", "create.email.required": "Email is required", "create.email.iataEmail": "Email should be @iata.org or @external.iata.org", "create.email.duplicatedUser": "User with such email already exists", "create.roles": "Roles", "create.roles.required": "Roles are required", "create.status": "Status", "create.status.required": "Status is required", "create.cancel": "Cancel", "create.submit": "Save", "update.title": "Update User", "update.submit": "Save"}, "pagination": {"itemsPerPage": "Records per page", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "rangeEmpty": "0 of 0", "rangeLabel": "{{start}} - {{end}} of {{length}}"}}