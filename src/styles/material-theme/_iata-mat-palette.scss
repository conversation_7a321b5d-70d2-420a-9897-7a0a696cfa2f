@use 'sass:map';

$dark-primary-text: rgba(#000000, 0.87);
$light-primary-text: #ffffff;

$iata-blue-weight-darker: 700;
$iata-blue-weight-primary: 600;
$iata-blue-weight-lighter: 100;

$iata-blue-palette: (
		50: #e8eafe,
		100: #d2d6fe,
		200: #a5adfd,
		300: #7884FC,
		400: #8973ff,
		500: #4B5BFB,
		600: #1E32FA,
		700: #0028f0,
		800: #0022eb,
		900: #0016e6,
		contrast: (
				50: $dark-primary-text,
				100: $dark-primary-text,
				200: $dark-primary-text,
				300: $light-primary-text,
				400: $light-primary-text,
				500: $light-primary-text,
				600: $light-primary-text,
				700: $light-primary-text,
				800: $light-primary-text,
				900: $light-primary-text,
		)
);

$iata-green-weight-darker: 900;
$iata-green-weight-primary: 700;
$iata-green-weight-lighter: 400;

$iata-green-palette: (
	50: #e8f6e8,
	100: #c7e9c8,
	200: #a3dba4,
	300: #7cce7f,
	400: #5ec363,
	500: #3fb846,
	600: #35a83d,
	700: #289632,
	800: #1a8526,
	900: #006610,

	contrast: (
		50: $dark-primary-text,
		100: $dark-primary-text,
		200: $dark-primary-text,
		300: $light-primary-text,
		400: $light-primary-text,
		500: $light-primary-text,
		600: $light-primary-text,
		700: $light-primary-text,
		800: $light-primary-text,
		900: $light-primary-text,
	)
);

$iata-red-weight-darker: 700;
$iata-red-weight-primary: 500;
$iata-red-weight-lighter: 100;

$iata-red-palette: (
	50: #ffebed,
	100: #fecdd1,
	200: #ed9a98,
	300: #e37370,
	400: #ec544d,
	500: #F04632,
	600: #e13b31,
	700: #cf312b,
	800: #c22a25,
	900: #b32019,

	contrast: (
		50: $dark-primary-text,
		100: $dark-primary-text,
		200: $dark-primary-text,
		300: $light-primary-text,
		400: $light-primary-text,
		500: $light-primary-text,
		600: $light-primary-text,
		700: $light-primary-text,
		800: $light-primary-text,
		900: $light-primary-text,
	)
);

$iata-yellow-weight-darker: 700;
$iata-yellow-weight-primary: 400;
$iata-yellow-weight-lighter: 100;

$iata-yellow-palette: (
	50: #fef8e2,
	100: #fdebb4,
	200: #fcdf85,
	300: #fbd354,
	400: #fac832,
	500: #f9bf1e,
	600: #f9b119,
	700: #f89f16,
	800: #f88f14,
	900: #f7700e,

	contrast: (
		50: $dark-primary-text,
		100: $dark-primary-text,
		200: $dark-primary-text,
		300: $light-primary-text,
		400: $light-primary-text,
		500: $light-primary-text,
		600: $light-primary-text,
		700: $light-primary-text,
		800: $light-primary-text,
		900: $light-primary-text,
	)
);

$iata-grey-weight-darker: 900;
$iata-grey-weight-primary: 700;
$iata-grey-weight-lighter: 400;

$iata-grey-palette: (
		50: #F5F5F5,
		100: #E2E4E8,
		200: #E6E6E6,
		300: #999999,
		400: #666666,
		500: #525252,
		600: #333333,
		700: #263238,
		800: #111111,
		900: #000000,
		contrast: (
				50: $dark-primary-text,
				100: $dark-primary-text,
				200: $dark-primary-text,
				300: $light-primary-text,
				400: $light-primary-text,
				500: $light-primary-text,
				600: $light-primary-text,
				700: $light-primary-text,
				800: $light-primary-text,
				900: $light-primary-text,
		)
);

@mixin convertScssPaletteToCss($palette, $prefix, $default: 500, $lighter: 100, $darker: 700) {
	$contrast-palette: ();

	@if map.has-key($palette, 'contrast') {
		$contrast-palette: map.get($palette, 'contrast');
		$palette: map.remove($palette, 'contrast');
	}

	@each $name, $color in $palette {
		--iata-#{$prefix}-#{$name}: #{$color};
	}

	@each $name, $color in $contrast-palette {
		--iata-#{$prefix}-#{$name}-contrast: #{$color};
	}

	--iata-#{$prefix}-primary: #{map.get($palette, $default)};
	--iata-#{$prefix}-primary-contrast: #{map.get($contrast-palette, $default)};
	--iata-#{$prefix}-lighter: #{map.get($palette, $lighter)};
	--iata-#{$prefix}-lighter-contrast: #{map.get($contrast-palette, $lighter)};
	--iata-#{$prefix}-darker: #{map.get($palette, $darker)};
	--iata-#{$prefix}-darker-contrast: #{map.get($contrast-palette, $darker)};
}
