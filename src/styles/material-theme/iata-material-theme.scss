@use '@angular/material' as mat;
@use 'iata-mat-palette' as palette;
@use 'iata-mat-buttons' as buttons;

$iata-mat-palette-primary: mat.m2-define-palette(
		palette.$iata-blue-palette,
		palette.$iata-blue-weight-primary,
		palette.$iata-blue-weight-lighter,
		palette.$iata-blue-weight-darker
);

$iata-mat-palette-accent: mat.m2-define-palette(
		palette.$iata-green-palette,
		palette.$iata-green-weight-primary,
		palette.$iata-green-weight-lighter,
		palette.$iata-green-weight-darker
);

$iata-mat-palette-warn: mat.m2-define-palette(
		palette.$iata-red-palette,
		palette.$iata-red-weight-primary,
		palette.$iata-red-weight-lighter,
		palette.$iata-red-weight-darker
);

$iata-typography: mat.m2-define-typography-config(
	$font-family: '"Aktiv Grotesk Corp", Roboto, sans-serif',
);

$iata-mat-theme: mat.m2-define-light-theme((
	color: (
		primary: $iata-mat-palette-primary,
		accent: $iata-mat-palette-accent,
		warn: $iata-mat-palette-warn,
	),
));

// convert all IATA SCSS palettes to CSS variables
:root {
	--iata-white: #ffffff;
	--iata-black: #000000;

	@include palette.convertScssPaletteToCss(
			palette.$iata-blue-palette, 'blue',
			palette.$iata-blue-weight-primary,
			palette.$iata-blue-weight-lighter,
			palette.$iata-blue-weight-darker
	);
	@include palette.convertScssPaletteToCss(
			palette.$iata-green-palette, 'green',
			palette.$iata-green-weight-primary,
			palette.$iata-green-weight-lighter,
			palette.$iata-green-weight-darker
	);
	@include palette.convertScssPaletteToCss(
			palette.$iata-red-palette, 'red',
			palette.$iata-red-weight-primary,
			palette.$iata-red-weight-lighter,
			palette.$iata-red-weight-darker
	);
	@include palette.convertScssPaletteToCss(
			palette.$iata-yellow-palette, 'yellow',
			palette.$iata-yellow-weight-primary,
			palette.$iata-yellow-weight-lighter,
			palette.$iata-yellow-weight-darker
	);
	@include palette.convertScssPaletteToCss(
			palette.$iata-grey-palette, 'grey',
			palette.$iata-grey-weight-primary,
			palette.$iata-grey-weight-lighter,
			palette.$iata-grey-weight-darker
	);
}

@include mat.all-component-typographies($iata-typography);
@include mat.elevation-classes();
@include mat.app-background();
@include mat.all-component-themes($iata-mat-theme);

@include buttons.iata-buttons-theme($iata-mat-theme);
