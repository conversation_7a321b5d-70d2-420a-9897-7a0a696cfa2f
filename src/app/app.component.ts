import { Component, OnInit } from '@angular/core';
import { AuthService } from '@shared/auth/auth.service';
import { MainFooterComponent } from './shared/components/layout/main-footer/main-footer.component';
import { RouterOutlet } from '@angular/router';
import { MainHeaderComponent } from './shared/components/layout/main-header/main-header.component';
import { BreadcrumbComponent } from './shared/components/breadcrumb/breadcrumb.component';
import { environment } from '@environments/environment';

@Component({
	selector: 'app-root',
	templateUrl: './app.component.html',
	styleUrls: ['./app.component.scss'],
	imports: [MainHeaderComponent, RouterOutlet, MainFooterComponent, BreadcrumbComponent],
})
export class AppComponent implements OnInit {
	constructor(private readonly authService: AuthService) {}

	ngOnInit() {
		// mock up login
		if (!this.authService.isLoggedIn()) {
			this.authService
				.login({
					userId: `${environment.userId}`,
					orgId: `${environment.orgId}`,
				})
				.subscribe({
					// eslint-disable-next-line
					next: () => console.log('login successfully...'),
					error: (err) => console.error('login failed...', err),
				});
		}
	}
}
