import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Response } from '@shared/models/response.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { Organization } from '../models/organization.model';
import { OrgInfo } from '../models/org-info.model';

@Injectable({ providedIn: 'root' })
export class OrgMgmtRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getOrgList(orgType?: string | null): Observable<Organization[]> {
		return super.getData<Response<Organization>>('org/org/list', { orgType: orgType ?? '' }).pipe(map((res) => res.data));
	}

	getOrgInfo(orgId: string): Observable<OrgInfo> {
		return super.getData<{ data: OrgInfo }>('org/org', { orgId }).pipe(map((res) => res.data));
	}
}
