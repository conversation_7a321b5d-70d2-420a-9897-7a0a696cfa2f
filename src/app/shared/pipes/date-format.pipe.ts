import { Pipe, PipeTransform } from '@angular/core';
import { DateTime } from 'luxon';

const DEFAULT_FORMAT = 'dd MMM yyyy';

type DateInput = string | number | DateTime | null | undefined;

/**
 * Pipe for general date/time formatting.
 *
 * It accepts value in several formats:
 *  - `string` - in the ISO format `yyyy-MM-dd'T'HH:mm:ss.SSS'Z'`
 *  - `number` - amount of milliseconds
 *  - `DateTime` - prepared Luxon DateTime object
 *
 * @format - you can change the format by passing this argument in the Luxon library format.
 */
@Pipe({
	name: 'iataDateFormat',
})
export class IataDateFormatPipe implements PipeTransform {
	public transform(value: DateInput, format: string = DEFAULT_FORMAT, isLocal = false): string {
		if (!value) {
			return '';
		}

		const dateTime = this.toDateTime(value);
		return this.formatDateTime(dateTime, format, isLocal);
	}

	private toDateTime(value: DateInput): DateTime {
		if (typeof value === 'number') {
			return DateTime.fromMillis(value, { zone: 'utc' });
		}
		if (value instanceof DateTime) {
			return value;
		}
		return DateTime.fromISO(value as string, { zone: 'utc' });
	}

	private formatDateTime(dateTime: DateTime, format: string, isLocal: boolean): string {
		return isLocal ? dateTime.toLocal().toFormat(format) : dateTime.toFormat(format);
	}
}
