import { DateTime } from 'luxon';
import { IataDateFormatPipe } from './date-format.pipe';

describe('IataDateFormatPipe', () => {
	let pipe: IataDateFormatPipe;

	beforeEach(() => {
		pipe = new IataDateFormatPipe();
	});

	it('should create an instance', () => {
		expect(pipe).toBeTruthy();
	});

	it('#transform should return an empty string for null or undefined input', () => {
		expect(pipe.transform(null)).toBe('');
		expect(pipe.transform(undefined)).toBe('');
	});

	it('#transform should format a string date correctly', () => {
		const input = '2023-05-15T12:30:00.000Z';
		const expected = '15 May 2023';
		
		expect(pipe.transform(input)).toBe(expected);
	});

	it('#transform should format a number (milliseconds) correctly', () => {
		const input = 1684150200000;
		const expected = '15 May 2023';

		expect(pipe.transform(input)).toBe(expected);
	});

	it('#transform should format a DateTime object correctly', () => {
		const input = DateTime.fromISO('2023-05-15T12:30:00.000Z');
		const expected = '15 May 2023';

		expect(pipe.transform(input)).toBe(expected);
	});

	it('#transform should use a custom format when provided', () => {
		const input = '2023-05-15T12:30:00.000Z';
		const format = 'yyyy-MM-dd HH:mm';
		const expected = '2023-05-15 12:30';

		expect(pipe.transform(input, format)).toBe(expected);
	});

	it('#transform should convert to local time when isLocal is true', () => {
		const input = '2023-05-15T12:30:00.000Z';
		const format = 'yyyy-MM-dd HH:mm';
		const localDateTime = DateTime.fromISO(input).toLocal();
		const expected = localDateTime.toFormat(format);

		expect(pipe.transform(input, format, true)).toBe(expected);
	});
});
