import { IataArrayMapByFieldPipe } from '@shared/pipes/array-map-by-field.pipe';

describe('IataArrayMapByFieldPipe', () => {
	const pipe = new IataArrayMapByFieldPipe();

	it('#transform should return default fields', () => {
		const items = [{ name: 'abc' }, { name: 'bcd' }, { name: 'cde' }];
		const result = pipe.transform(items);
		expect(result).toHaveSize(3);
		expect(result[2]).toEqual('cde');
	});

	it('#transform should return particular fields', () => {
		const items = [{ value: 'abc' }, { value: 'bcd' }, { value: 'cde' }];
		const result = pipe.transform(items, 'value');
		expect(result).toHaveSize(3);
		expect(result[2]).toEqual('cde');
	});

	it('#transform should return empty result if specified property does not exist', () => {
		const items = [{ name: 'abc' }, { name: 'bcd' }, { name: 'cde' }];
		const result = pipe.transform(items, 'value');
		expect(result).toHaveSize(0);
	});

	it('#transform should return empty result for an empty input', () => {
		const result = pipe.transform([]);
		expect(result).toHaveSize(0);
	});
});
