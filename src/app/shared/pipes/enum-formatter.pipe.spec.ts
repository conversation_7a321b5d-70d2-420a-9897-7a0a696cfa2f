import { EnumFormatterPipe } from './enum-formatter.pipe';

describe('EnumFormatterPipe', () => {
	const pipe = new EnumFormatterPipe();

	it('should handle empty value', () => {
		expect(pipe.transform('')).toBe('');
	});

	it('should handle enum values with capital letters', () => {
		expect(pipe.transform('AUDITOR')).toEqual('Auditor');
	});

	it('should handle enum balues with underscore', () => {
		expect(pipe.transform('LEAD_AUDITOR')).toEqual('Lead Auditor');
	});
});
