import { FileSizeFormatPipe } from '@shared/pipes/file-size-format.pipe';

describe('FileSizeFormatPipe', () => {
	const pipe = new FileSizeFormatPipe();

	it('#transform should format value to Bytes', () => {
		expect(pipe.transform(123)).toBe('123 B');
	});

	it('#transform should format value to Kilo Bytes', () => {
		expect(pipe.transform(2096)).toBe('2.05 KB');
	});

	it('#transform should format value to Mega Bytes', () => {
		expect(pipe.transform(3732930)).toBe('3.56 MB');
	});

	it('#transform should format value to Giga Bytes', () => {
		expect(pipe.transform(2662879723)).toBe('2.48 GB');
	});
});
