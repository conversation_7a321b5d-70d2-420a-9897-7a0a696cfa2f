import { Pipe, PipeTransform } from '@angular/core';
import { isObservable, Observable, of } from 'rxjs';
import { catchError, map, startWith } from 'rxjs/operators';

export interface LoadingTrackerPayload {
	loading: boolean;
	value?: any;
	error?: any;
}

@Pipe({
	name: 'asyncLoadingTracker',
})
export class AsyncLoadingTrackerPipe implements PipeTransform {
	transform(val: any): Observable<LoadingTrackerPayload> {
		return isObservable(val)
			? val.pipe(
					map((value: any) => ({ loading: false, value })),
					startWith({ loading: true }),
					catchError((error) => of({ loading: false, error }))
				)
			: val;
	}
}
