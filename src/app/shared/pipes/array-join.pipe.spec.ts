import { IataArrayJoinPipe } from '@shared/pipes/array-join.pipe';

describe('JoinPipe ', () => {
	let pipe: IataArrayJoinPipe;

	beforeEach(() => {
		pipe = new IataArrayJoinPipe();
	});

	it(`#transform should return 'abcd'`, () => {
		expect(pipe.transform(['a', 'b', 'c', 'd'], '')).toEqual('abcd');
	});

	it('transform should return abc123', () => {
		expect(pipe.transform(['a', 'b', 'c', 1, 2, 3], '')).toEqual('abc123');
	});

	it('#transform should return 1a2a3', () => {
		expect(pipe.transform([1, 2, 3], 'a')).toEqual('1a2a3');
	});

	it('#transform should return the value unchanged', () => {
		expect(pipe.transform('a', '')).toEqual('a');
	});
});
