import { AsyncLoadingTrackerPipe, LoadingTrackerPayload } from '@shared/pipes/async-loading-tracker.pipe';
import { first, isObservable, of, skip } from 'rxjs';

describe('AsyncLoadingTrackerPipe', () => {
	const pipe = new AsyncLoadingTrackerPipe();

	it('#transform should return the same value if not Observable', () => {
		const value = 'test';
		const actual = pipe.transform(value) as any;
		expect(isObservable(actual)).toBeFalse();
		expect(actual).toEqual(value);
	});

	it('#transform should return proper turn on loading flag', () => {
		const value = 'test';
		pipe.transform(of(value))
			.pipe(first())
			.subscribe((actual: LoadingTrackerPayload) => {
				expect(actual.loading).toBeTrue();
			});
	});

	it('#transform should return proper value when loading finished', () => {
		const value = 'test';
		pipe.transform(of(value))
			.pipe(skip(1))
			.subscribe((actual: LoadingTrackerPayload) => {
				expect(actual.value).toEqual(value);
			});
	});
});
