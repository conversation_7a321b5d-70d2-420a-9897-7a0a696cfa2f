import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
	name: 'iataEnumFormatter',
})
export class EnumFormatterPipe implements PipeTransform {
	transform(value: string): string {
		if (!value) {
			return '';
		}

		return this.titleCase(value.toLowerCase().split('_').join(' '));
	}

	private titleCase(word: string): string {
		return word.replace(/(^|\s)\S/g, (text) => {
			return text.toUpperCase();
		});
	}
}
