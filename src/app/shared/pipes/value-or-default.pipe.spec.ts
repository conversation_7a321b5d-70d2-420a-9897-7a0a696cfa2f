import { ValueOrDefaultPipe } from './value-or-default.pipe';

describe('ValueOrDefaultPipe', () => {
	let pipe: ValueOrDefaultPipe;

	beforeEach(() => {
		pipe = new ValueOrDefaultPipe();
	});

	it('#transform should return the default value when input is undefined', () => {
		const result = pipe.transform(undefined, 'default');
		expect(result).toBe('default');
	});

	it('#transform should return the default value when input is null', () => {
		const result = pipe.transform(null, 'default');
		expect(result).toBe('default');
	});

	it('#transform should return the string representation of the input value when it is a number', () => {
		const result = pipe.transform(100);
		expect(result).toBe('100');
	});

	it('#transform should return the input value when it is a non-empty string', () => {
		const result = pipe.transform('test');
		expect(result).toBe('test');
	});

	it('#transform should return the default value when input is undefined and no default is provided', () => {
		const result = pipe.transform(undefined);
		expect(result).toBe('-');
	});
});
