import { Pipe, PipeTransform } from '@angular/core';

const KB_DIVIDER = 1024;
const MB_DIVIDER = 1024 * 1024;
const GB_DIVIDER = 1024 * 1024 * 1024;

/**
 * Formats file size in bytes to a human-readable value.
 *
 * @return - File size in B, KB, MB, or GB.
 */
@Pipe({
	name: 'iataFileSizeFormat',
})
export class FileSizeFormatPipe implements PipeTransform {
	transform(sizeInBytes: number): string {
		if (sizeInBytes < KB_DIVIDER) {
			return `${sizeInBytes} B`;
		} else if (sizeInBytes < MB_DIVIDER) {
			return `${(sizeInBytes / KB_DIVIDER).toFixed(2)} KB`;
		} else if (sizeInBytes < GB_DIVIDER) {
			return `${(sizeInBytes / MB_DIVIDER).toFixed(2)} MB`;
		} else {
			return `${(sizeInBytes / GB_DIVIDER).toFixed(2)} GB`;
		}
	}
}
