import { CodeName } from '@shared/models/code-name.model';
import { isCodeName, isOfType, isString } from './type.utils';

describe('Type Utils', () => {
	it('#isCodeName should return true for a valid CodeName object', () => {
		const validCodeName: CodeName = { code: '123', name: 'Test' };
		expect(isCodeName(validCodeName)).toBe(true);
	});

	it('#isCodeName should return true for a CodeName object with additional properties', () => {
		const extendedCodeName = { code: '123', name: 'Test', extraProp: 'extra' };
		expect(isCodeName(extendedCodeName)).toBe(true);
	});

	it('#isCodeName should return false for an object missing the code property', () => {
		const invalidCodeName = { name: 'Test' };
		expect(isCodeName(invalidCodeName as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for an object missing the name property', () => {
		const invalidCodeName = { code: '123' };
		expect(isCodeName(invalidCodeName as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for an empty object', () => {
		expect(isCodeName({} as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for a string', () => {
		expect(isCodeName('not a CodeName')).toBe(false);
	});

	it('#isCodeName should return false for null', () => {
		expect(isCodeName(null as unknown as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for undefined', () => {
		expect(isCodeName(undefined as unknown as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for a number', () => {
		expect(isCodeName(123 as unknown as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for an array', () => {
		expect(isCodeName([] as unknown as CodeName)).toBe(false);
	});

	it('#isCodeName should return false for a boolean', () => {
		expect(isCodeName(true as unknown as CodeName)).toBe(false);
	});

	it('#isCodeName should return true for a CodeName object with empty string values', () => {
		const emptyStringCodeName: CodeName = { code: '', name: '' };
		expect(isCodeName(emptyStringCodeName)).toBe(true);
	});

	it('#isCodeName should return false for a CodeName object with undefined values', () => {
		const undefinedValuesCodeName = { code: undefined, name: undefined };
		expect(isCodeName(undefinedValuesCodeName as unknown as CodeName)).toBe(false);
	});

	it('#isString should return true for an empty string', () => {
		expect(isString('')).toBe(true);
	});

	it('#isString should return true for a non-empty string', () => {
		expect(isString('hello')).toBe(true);
	});

	it('#isString should return true for a string with spaces', () => {
		expect(isString('  ')).toBe(true);
	});

	it('#isString should return true for a string with numbers', () => {
		expect(isString('123')).toBe(true);
	});

	it('#isString should return false for a number', () => {
		expect(isString(123)).toBe(false);
	});

	it('#isString should return false for a boolean', () => {
		expect(isString(true)).toBe(false);
		expect(isString(false)).toBe(false);
	});

	it('#isString should return false for null', () => {
		expect(isString(null)).toBe(false);
	});

	it('#isString should return false for undefined', () => {
		expect(isString(undefined)).toBe(false);
	});

	it('#isString should return false for an object', () => {
		expect(isString({})).toBe(false);
	});

	it('#isString should return false for an array', () => {
		expect(isString([])).toBe(false);
	});

	it('#isString should return false for a function', () => {
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		expect(isString(() => { })).toBe(false);
	});

	it('#isString should return false for a Symbol', () => {
		expect(isString(Symbol('test'))).toBe(false);
	});

	it('#isString should return false for a BigInt', () => {
		expect(isString(BigInt(123))).toBe(false);
	});

	it('#isOfType should return false for objects missing keys', () => {
		const obj = { name: 'John' };
		const result = isOfType<CodeName>(obj, ['code', 'name']);
		expect(result).toBe(false);
	});

	it('#isOfType should return false for objects with extra keys', () => {
		const obj = { name: 'John', age: 30, extraKey: 'value' };
		const result = isOfType<CodeName>(obj, ['code', 'name']);
		expect(result).toBe(false);
	});

	it('#isOfType should return false for non-object values', () => {
		const obj = 'string';
		const result = isOfType<CodeName>(obj, ['code', 'name']);
		expect(result).toBe(false);
	});

	it('#isOfType should return true for objects with keys in the same order', () => {
		const obj = { name: 'Alice', code: '25' };
		const result = isOfType<CodeName>(obj, ['code', 'name']);
		expect(result).toBe(true);
	});

	it('#isOfType should return false for objects with keys in a different order', () => {
		const obj = { age: 25, name: 'Alice' };
		const result = isOfType<CodeName>(obj, ['code', 'name']);
		expect(result).toBe(false);
	});
});
