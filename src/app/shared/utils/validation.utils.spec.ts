import { FormControl } from '@angular/forms';
import { codeNameValidator, dateBeforeTodayValidator, endDateAfterStartDateValidator, typeValidator } from './validation.utils';
import { DateTime } from 'luxon';

describe('Validation Utils', () => {
	it('#codeNameValidator should return null for a null as the control value', () => {
		const control = new FormControl(null);
		const validator = codeNameValidator();

		expect(validator(control)).toBeNull();
	});

	it('#codeNameValidator should return null for an empty string as the control value', () => {
		const control = new FormControl('');
		const validator = codeNameValidator();

		expect(validator(control)).toBeNull();
	});

	it('#codeNameValidator should return null for valid CodeName object', () => {
		const control = new FormControl({ code: '123', name: 'Test' });
		const validator = codeNameValidator();

		expect(validator(control)).toBeNull();
	});

	it('#codeNameValidator should return error for invalid CodeName object', () => {
		const control = new FormControl({ code: '123' });
		const validator = codeNameValidator();

		expect(validator(control)).toEqual({ invalidCodeName: true });
	});

	it('#codeNameValidator should return error for invalid control value', () => {
		const control = new FormControl('Some search param');
		const validator = codeNameValidator();

		expect(validator(control)).toEqual({ invalidCodeName: true });
	});

	it('#typeValidator should return null if the value is of correct type', () => {
		const control = new FormControl({ id: 1, name: 'John' });
		const validator = typeValidator<{ id: number; name: string }>(['id', 'name']);
		const result = validator(control);

		expect(result).toBeNull();
	});

	it('#typeValidator should return invalidType error if the value is of incorrect type', () => {
		const control = new FormControl('Some search param');
		const validator = typeValidator<{ id: number; name: string }>(['id', 'name']);
		const result = validator(control);

		expect(result).toEqual({ invalidType: true });
	});

	it('#typeValidator should return null if value is undefined', () => {
		const control = new FormControl();

		const validator = typeValidator<{ id: number; name: string }>(['id', 'name']);
		const result = validator(control);

		expect(result).toBeNull();
	});

	it('#endDateAfterStartDateValidator should return null if start date or end date is not set or valid', () => {
		const now = DateTime.now();
		expect(
			endDateAfterStartDateValidator()(
				new FormControl({
					startDate: new FormControl(now.minus({ days: 2 })),
					endDate: new FormControl(now.minus({ days: 1 })),
				})
			)
		).toBeNull();
		expect(
			endDateAfterStartDateValidator()(
				new FormControl({
					startDate: null,
					endDate: new FormControl(now.minus({ days: 1 })),
				})
			)
		).toBeNull();
		expect(
			endDateAfterStartDateValidator()(
				new FormControl({
					startDate: new FormControl(now.minus({ days: 2 })),
					endDate: null,
				})
			)
		).toBeNull();
	});

	it('#endDateAfterStartDateValidator should return null if start date is before end date', () => {
		const now = DateTime.now();
		const control = new FormControl({
			startDate: new FormControl(now.minus({ days: 2 })),
			endDate: new FormControl(now.minus({ days: 1 })),
		});

		expect(endDateAfterStartDateValidator()(control)).toBeNull();
	});

	it('#endDateAfterStartDateValidator should return error if start date is after end date', () => {
		const now = DateTime.now();
		const control = new FormControl({
			startDate: new FormControl(now.minus({ days: 1 })),
			endDate: new FormControl(now.minus({ days: 2 })),
		});

		expect(endDateAfterStartDateValidator()(control)).toBeNull();
	});

	it('#dateBeforeTodayValidator should return null if selected date is before today', () => {
		expect(dateBeforeTodayValidator()(new FormControl(DateTime.now().minus({ days: 1 })))).toBeNull();
	});

	it('#dateBeforeTodayValidator should return error if selected date is after today', () => {
		expect(dateBeforeTodayValidator()(new FormControl(DateTime.now().plus({ days: 1 })))).not.toBeNull();
	});
});
