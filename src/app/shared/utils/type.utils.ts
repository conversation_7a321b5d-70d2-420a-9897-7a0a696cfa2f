import { CodeName } from '@shared/models/code-name.model';

export function isString(x: any): x is string {
	return typeof x === 'string';
}

export function isCodeName(value: string | CodeName): value is CodeName {
	return (value as CodeName)?.code !== undefined && (value as CodeName)?.name !== undefined;
}

export function isOfType<T>(obj: any, keys: (keyof T)[]): obj is T {
	if (typeof obj === 'object' && obj !== null) {
		const sortedKeys = Object.keys(obj).sort((a, b) => a.localeCompare(b));
		const sortedExpectedKeys = keys
			.map((key) => String(key))
			.sort((a, b) => a.localeCompare(b));

		return sortedKeys.join(',') === sortedExpectedKeys.join(',');
	}

	return false;
}