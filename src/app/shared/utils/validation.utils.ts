import { DateTime } from 'luxon';
import { AbstractControl, FormArray, FormControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isCodeName, isOfType } from './type.utils';

export const INTEGER_NUMBER_REGEXP = /^\d+$/;
export const FLOATING_OR_INTEGER_NUMBER_REGEXP = /^(\d+)?(\d+\.\d+)?$/;
export const ALPHANUMERIC_REGEXP = /^\w+$/;

export function updateControlErrors(isValid: boolean, control: AbstractControl, errorKey: string): ValidationErrors | null {
	if (isValid) {
		control.setErrors(null);
		return null;
	} else {
		control.setErrors({ [errorKey]: true });
		return { [errorKey]: true };
	}
}

export function updateArrayControlErrors(isValid: boolean, control: AbstractControl, errorKey: string): ValidationErrors | null {
	if (isValid) {
		const resultingErrors = Object.keys(control.errors ?? {})
			.filter((key: string) => key !== errorKey)
			.reduce((acc: any, currentKey: string) => {
				acc[currentKey] = control.errors![currentKey];
				return acc;
			}, {});

		if (Object.keys(resultingErrors).length > 0) {
			control.setErrors(resultingErrors);
			return resultingErrors;
		} else {
			control.setErrors(null);
			return null;
		}
	} else {
		control.setErrors({ ...(control.errors ?? {}), [errorKey]: true });
		return { [errorKey]: true };
	}
}

export function duplicatedValueValidator(existingValues: string[]): ValidatorFn {
	return (control: AbstractControl): ValidationErrors | null => {
		existingValues = existingValues.map((item: string) => item.toLowerCase());
		const value = control.value || '';
		const isValid = value?.length === 0 || !existingValues.includes(value.toLowerCase());
		return updateControlErrors(isValid, control, 'duplicated');
	};
}

export function duplicatedCombinationValueValidator(
	form: AbstractControl,
	secondFieldKey: string,
	existingValues: string[],
	invertValues: boolean
): ValidatorFn {
	return (primaryControl: AbstractControl): ValidationErrors | null => {
		existingValues = existingValues.map((item: string) => item.toLowerCase());
		const firstValue = primaryControl.value ?? '';
		const secondValue = form.get(secondFieldKey)?.value ?? '';
		const value = invertValues ? `${secondValue}${firstValue}` : `${firstValue}${secondValue}`;
		const isValid = value?.length === 0 || !existingValues.includes(value.toLowerCase());
		return updateControlErrors(isValid, primaryControl, 'duplicated');
	};
}

export function formArrayMinLengthValidator(minLength: number, arrayItemFieldName: string): ValidatorFn {
	return (formArray: AbstractControl): ValidationErrors | null => {
		const isValid = (formArray as FormArray).length >= minLength;
		const validationControl =
			(formArray as FormArray).length > 0 ? ((formArray as FormArray).controls[0].get(arrayItemFieldName) as FormControl) : formArray;
		return updateArrayControlErrors(isValid, validationControl, 'formArrayMinLength');
	};
}

export function formArrayMaxLengthValidator(maxLength: number, arrayItemFieldName: string): ValidatorFn {
	return (formArray: AbstractControl): ValidationErrors | null => {
		const isValid = (formArray as FormArray).controls.length <= maxLength;
		const validationControl =
			(formArray as FormArray).length > 0 ? ((formArray as FormArray).controls[0].get(arrayItemFieldName) as FormControl) : formArray;
		return updateArrayControlErrors(isValid, validationControl, 'formArrayMaxLength');
	};
}

export function dependentFieldValidator(
	dependentFieldPath: string,
	validatedFieldPath: string,
	predicate: (dependentFieldValue: any) => boolean,
	validator: ValidatorFn,
	errorNamespace: string
): ValidatorFn {
	return (formControl) => {
		if (!formControl.parent) {
			return null;
		}

		const dependentFieldControl = formControl.get(dependentFieldPath);
		const validatedFieldControl = formControl.get(validatedFieldPath);

		let error = null;
		if (predicate(dependentFieldControl?.value)) {
			error = validator(validatedFieldControl!);
		}

		return updateControlErrors(!error, validatedFieldControl!, errorNamespace);
	};
}

export function codeNameValidator(): ValidatorFn {
	return (control: AbstractControl): ValidationErrors | null => {
		const value = control.value;

		if (value && !isCodeName(value)) {
			return { invalidCodeName: true };
		}

		return null;
	};
}

export function typeValidator<T>(keys: (keyof T)[]): ValidatorFn {
	return (control: AbstractControl): ValidationErrors | null => {
		const value = control.value;

		if (value && !isOfType<T>(value, keys)) {
			return { invalidType: true };
		}

		return null;
	};
}

export function endDateAfterStartDateValidator(startDatePath = 'startDate', endDatePath = 'endDate'): ValidatorFn {
	return (form: AbstractControl): ValidationErrors | null => {
		const startDate = getDateTime(form.get(startDatePath)?.value);
		const endDate = getDateTime(form.get(endDatePath)?.value);

		// date range should be validated only if both dates were set and valid
		if (!startDate.isValid || !endDate.isValid) {
			return null;
		}

		const isValid = endDate.diff(startDate, 'days').toObject().days! >= 0;
		return updateControlErrors(isValid, form.get(endDatePath)!, 'endDateAfterStartDate');
	};
}

export function dateBeforeTodayValidator(): ValidatorFn {
	return (control: AbstractControl): ValidationErrors | null => {
		const todayDate = DateTime.now();
		const date = getDateTime(control.value);

		if (todayDate < date) {
			control.setErrors({ dateIsAfterToday: true });
			return { dateIsAfterToday: true };
		}

		control.setErrors(null);
		return null;
	};
}

function getDateTime(value: string | DateTime): DateTime {
	return value instanceof DateTime ? value : DateTime.fromISO(value, { zone: 'utc' });
}
