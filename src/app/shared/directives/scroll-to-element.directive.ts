import { Directive, HostListener, Input } from '@angular/core';

@Directive({
	selector: '[scrollToElement]',
})
export class ScrollToElementDirective {
	@Input() scrollToElement!: string;
	@Input() block: ScrollLogicalPosition = 'start';

	@HostListener('click', ['$event.target']) onClick(element: HTMLElement) {
		const target = document.getElementById(this.scrollToElement);

		if (element.closest('[data-scroll-trigger]') && target) {
			target.scrollIntoView({ behavior: 'smooth', block: this.block });
		}
	}
}
