import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
	selector: '[formControlName]',
})
export class FormControlPatchDirective {
	constructor(private ngControl: NgControl) {}

	@HostListener('blur')
	onBlur(): void {
		if (!this.ngControl.control?.value) {
			// force value change to re-check the validation errors
			this.ngControl.control?.setValue(this.ngControl.control?.value);
		}
	}
}
