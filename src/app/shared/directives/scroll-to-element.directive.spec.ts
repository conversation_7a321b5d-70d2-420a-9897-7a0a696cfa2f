
import { Component, DebugElement } from '@angular/core';
import { ScrollToElementDirective } from './scroll-to-element.directive';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';

@Component({
    template: `<div [scrollToElement]="target">
		<div id="target">Some text</div>
		<p>Some other text</p>
		<button>Save</button>
	</div>`,
    imports: [ScrollToElementDirective]
})
class TestComponent { }

describe('ScrollToBottomDirective', () => {
	let fixture: ComponentFixture<TestComponent>;
	let container: DebugElement[];

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [ScrollToElementDirective, TestComponent],
		}).compileComponents();

		fixture = TestBed.createComponent(TestComponent);
		container = fixture.debugElement.queryAll(By.directive(ScrollToElementDirective));
		fixture.detectChanges();
	});

	it('should have one container with directive applied to it', () => {
		expect(container.length).toBe(1);
	});

	it('should scroll to the element when element with data-scroll-trigger attribute is clicked', () => {
		const button: HTMLButtonElement = fixture.debugElement.query(By.css('button')).nativeElement;
		button.setAttribute('data-scroll-trigger', '');
		const target: HTMLElement = fixture.debugElement.queryAll(By.css('div'))[1].nativeElement;
		spyOn(document, 'getElementById').and.returnValue(target);
		spyOn(target, 'scrollIntoView');

		button.click();
		fixture.detectChanges();

		expect(target.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
	});

	it('should not scroll to the element when element without data-scroll-trigger attribute is clicked', () => {
		const button: HTMLButtonElement = fixture.debugElement.query(By.css('button')).nativeElement;
		const target: HTMLElement = fixture.debugElement.queryAll(By.css('div'))[1].nativeElement;
		spyOn(document, 'getElementById').and.returnValue(target);
		spyOn(target, 'scrollIntoView');

		button.click();
		fixture.detectChanges();

		expect(target.scrollIntoView).not.toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
	});
});
