import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA, ElementRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { noop, of } from 'rxjs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			providers: [
				{
					provide: Router,
					useClass: class RouterMock {
						get events() {
							return of(true);
						}
					},
				},
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: ElementRef,
					useClass: class ElementRefMock extends ElementRef {
						constructor() {
							super(undefined);
						}
						override nativeElement = {
							contains: noop,
						};
					},
				},
				{
					provide: TranslateService,
					useClass: class TranslateServiceMock {},
				},
			],
			imports: [TranslateModule.forRoot()],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		await TestBed.overrideComponent(MainHeaderComponent, {
			set: {
				schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			},
		});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('#ngOnInit should listen router events', () => {
		const router = TestBed.inject(Router);
		spyOnProperty(router, 'events').and.returnValue(of(new NavigationEnd(0, '', '')));

		component.ngOnInit();
	});
});
