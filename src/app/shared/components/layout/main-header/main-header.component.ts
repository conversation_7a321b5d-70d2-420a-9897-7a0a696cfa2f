import { ChangeDetectionStrategy, Component, NO_ERRORS_SCHEMA, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { filter } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'iata-main-header',
	templateUrl: './main-header.component.html',
	styleUrls: ['./main-header.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatIconModule, MatTooltipModule, RouterLink, RouterLinkActive, TranslateModule],
	schemas: [NO_ERRORS_SCHEMA],
})
export class MainHeaderComponent extends DestroyRefComponent implements OnInit {
	constructor(private readonly router: Router) {
		super();
	}

	ngOnInit(): void {
		this.router.events.pipe(
			filter((event) => event instanceof NavigationEnd),
			takeUntilDestroyed(this.destroyRef)
		);
	}
}
