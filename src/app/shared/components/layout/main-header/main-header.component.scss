@use 'utils';

$header-height: 80px;

.iata-main-header {
	height: $header-height;
	background-color: var(--iata-white);

	&__content {
		height: 100%;
		display: flex;
		justify-content: space-between;
	}

	&__logo-link {
		color: var(--iata-blue-primary);
		font-size: 16px;
		display: flex;
		align-items: center;
		font-weight: 500;
		text-decoration: none;
		white-space: nowrap;
	}

	&__logo {
		width: $header-height;
		height: $header-height;
	}

	&__title {
		font-weight: 800;
	}

	&__subtitle {
		display: block;
		font-weight: 400;
	}

	&__nav-container {
		display: flex;
		justify-content: space-between;
		width: 100%;
		margin-left: 30px;
	}

	&__primary-nav {
		display: flex;
		height: 100%;
		align-items: center;

		&__list {
			margin: 0;
			padding: 0;
			list-style-type: none;

			&__item {
				display: inline-block;
				margin-right: 40px;
				position: relative;

				&:last-of-type {
					margin-right: 0;
				}

				&__link {
					font-size: 16px;
					line-height: 26px;
					font-weight: 500;
					cursor: pointer;
					color: var(--iata-grey-primary);
					text-decoration: none;

					&:visited {
						color: var(--iata-grey-primary);
					}

					&__icon {
						position: absolute;
					}

					&__label {
						padding-left: 30px;
						user-select: none;
					}

					&:hover,
					&:active,
					&.iata-active-nav-item {
						color: var(--iata-blue-primary);
					}

					&.iata-active-nav-item {
						pointer-events: none;
					}
				}
			}
		}
	}

	&__secondary-nav {
		&__list {
			margin: 0;
			padding: 0;
			list-style-type: none;
			display: flex;
			align-items: center;
			height: 100%;

			&__item {
				cursor: pointer;
				font-size: 20px;
				text-transform: capitalize;
				margin: 0 10px;
				user-select: none;
				color: var(--iata-grey-primary);

				&:last-of-type {
					margin-right: 0;
				}

				&__link {
					cursor: pointer;
					color: var(--iata-grey-primary);

					&:visited {
						color: var(--iata-grey-primary);
					}

					&__label {
						padding-left: 30px;
						user-select: none;
					}

					&:hover,
					&:active,
					&.iata-active-nav-item {
						color: var(--iata-blue-primary);
					}

					&.iata-active-nav-item {
						pointer-events: none;
					}
				}
			}
		}
	}
}

@include utils.respondTo(utils.$device-breakpoint-large) {
	.iata-main-header {
		width: utils.$device-breakpoint-large;
	}
}
