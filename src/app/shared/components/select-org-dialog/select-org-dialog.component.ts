import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Organization } from '@shared/models/organization.model';
import { OrgType } from '@shared/models/org-type.model';
import { NotificationService } from '@shared/services/notification.service';

@Component({
	selector: 'orll-select-org-dialog',
	templateUrl: './select-org-dialog.component.html',
	styleUrl: './select-org-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatDialogModule, MatListModule, TranslateModule],
})
export class SelectOrgDialogComponent extends DestroyRefComponent implements OnInit {
	orgList: Organization[] = [];
	constructor(
		public readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly notificationService: NotificationService,
		private readonly translate: TranslateService,
		private readonly cdr: ChangeDetectorRef,
		public dialogRef: MatDialogRef<SelectOrgDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	ngOnInit(): void {
		const orgType = this.data.orgType;

		this.orgMgmtRequestService
			.getOrgList(orgType)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					if (!orgType) {
						this.orgList = res.filter((item) => item.orgType !== OrgType.SHIPPER && item.orgType !== OrgType.CONSIGNEE);
					} else {
						this.orgList = res;
					}

					this.cdr.markForCheck();
				},
				error: (err) => {
					this.notificationService.showError(this.translate.instant('common.dialog.orglist.fail') + `: ${err.message}`);
				},
			});
	}

	getOrgInfo(orgId: string): void {
		this.orgMgmtRequestService
			.getOrgInfo(orgId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dialogRef.close(res);
					this.cdr.markForCheck();
				},
				error: (err) => {
					this.notificationService.showError(this.translate.instant('common.dialog.orginfo.fail') + `: ${err.message}`);
				},
			});
	}
}
