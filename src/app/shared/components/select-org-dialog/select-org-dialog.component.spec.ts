import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SelectOrgDialogComponent } from './select-org-dialog.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { ChangeDetectorRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { TranslateModule } from '@ngx-translate/core';
import { of, throwError } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { OrgInfo } from '@shared/models/org-info.model';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { NotificationService } from '@shared/services/notification.service';

describe('SelectOrgDialogComponent', () => {
	let component: SelectOrgDialogComponent;
	let fixture: ComponentFixture<SelectOrgDialogComponent>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<SelectOrgDialogComponent>>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	const mockOrganizations: Organization[] = [
		{ id: '1', name: 'Org 1', orgType: 'SHP' },
		{ id: '2', name: 'Org 2', orgType: 'CNE' },
		{ id: '3', name: 'Org 3', orgType: 'AIRLINE' },
		{ id: '4', name: 'Org 4', orgType: 'FREIGHT_FORWARDER' },
	];

	const mockOrgInfo: OrgInfo = {
		id: '3',
		companyName: 'Org 3',
		partyRole: 'AIRLINE',
		countryCode: 'US',
		locationName: 'New York',
		regionCode: 'NY',
		textualPostCode: '10001',
		cityCode: 'NYC',
		persons: [],
	};

	// Setup for tests with orgType specified
	describe('with orgType specified', () => {
		beforeEach(async () => {
			mockOrgMgmtRequestService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getOrgInfo']);
			mockNotificationService = jasmine.createSpyObj<NotificationService>('NotificationService', ['showError', 'showSuccess']);
			mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
			mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

			await TestBed.configureTestingModule({
				imports: [MatDialogModule, MatListModule, TranslateModule.forRoot(), NoopAnimationsModule],
				providers: [
					{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
					{ provide: NotificationService, useValue: mockNotificationService },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: { orgType: 'AIRLINE' } },
				],
			}).compileComponents();

			fixture = TestBed.createComponent(SelectOrgDialogComponent);
			component = fixture.componentInstance;
		});

		it('should create', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();
			expect(component).toBeTruthy();
		});

		it('should fetch organizations with specified orgType', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();

			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalledWith('AIRLINE');
			expect(component.orgList).toEqual(mockOrganizations);
		});

		it('should show error snackbar when getOrgList fails', () => {
			const error = { message: 'Network error' };
			mockOrgMgmtRequestService.getOrgList.and.returnValue(throwError(() => error));
			fixture.detectChanges();

			expect(mockNotificationService.showError).toHaveBeenCalledWith(`common.dialog.orglist.fail: ${error.message}`);
		});
	});

	// Setup for tests with no orgType specified
	describe('with no orgType specified', () => {
		beforeEach(async () => {
			mockOrgMgmtRequestService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getOrgInfo']);
			mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
			mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

			await TestBed.configureTestingModule({
				imports: [MatDialogModule, MatListModule, TranslateModule.forRoot(), NoopAnimationsModule],
				providers: [
					{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: { orgType: null } },
				],
			}).compileComponents();

			fixture = TestBed.createComponent(SelectOrgDialogComponent);
			component = fixture.componentInstance;
		});

		it('should filter out SHP and CNE organizations when no orgType is specified', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();

			const filteredOrgs = mockOrganizations.filter((org) => org.orgType !== 'SHP' && org.orgType !== 'CNE');
			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalledWith(null);
			expect(component.orgList).toEqual(filteredOrgs);
		});
	});

	describe('getOrgInfo', () => {
		beforeEach(async () => {
			mockOrgMgmtRequestService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getOrgInfo']);
			mockNotificationService = jasmine.createSpyObj<NotificationService>('NotificationService', ['showError', 'showSuccess']);
			mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
			mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

			await TestBed.configureTestingModule({
				imports: [MatDialogModule, MatListModule, TranslateModule.forRoot(), NoopAnimationsModule],
				providers: [
					{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
					{ provide: NotificationService, useValue: mockNotificationService },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: { orgType: 'AIRLINE' } },
				],
			}).compileComponents();

			fixture = TestBed.createComponent(SelectOrgDialogComponent);
			component = fixture.componentInstance;
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();
		});

		it('should fetch organization info and close dialog with result', () => {
			mockOrgMgmtRequestService.getOrgInfo.and.returnValue(of(mockOrgInfo));

			component.getOrgInfo('3');

			expect(mockOrgMgmtRequestService.getOrgInfo).toHaveBeenCalledWith('3');
			expect(mockDialogRef.close).toHaveBeenCalledWith(mockOrgInfo);
		});

		it('should show error snackbar when getOrgInfo fails', () => {
			const error = { message: 'Network error' };
			mockOrgMgmtRequestService.getOrgInfo.and.returnValue(throwError(() => error));

			component.getOrgInfo('3');

			expect(mockNotificationService.showError).toHaveBeenCalledWith(`common.dialog.orginfo.fail: ${error.message}`);
		});
	});
});
