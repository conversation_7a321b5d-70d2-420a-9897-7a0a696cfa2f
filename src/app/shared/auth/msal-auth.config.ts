import { BrowserCacheLocation, InteractionType, PublicClientApplication } from '@azure/msal-browser';
import { MsalGuardConfiguration } from '@azure/msal-angular/msal.guard.config';
import { MsalInterceptorConfiguration } from '@azure/msal-angular/msal.interceptor.config';
import { environment } from '@environments/environment';
import { LogLevel } from '@azure/msal-common/dist/logger/Logger';

const IS_INTERNET_EXPLORER = window.navigator.userAgent.indexOf('MSIE ') > -1 || window.navigator.userAgent.indexOf('Trident/') > -1;
// TODO: update scope and MSAL environment variables
const MSAL_AUTH_SCOPES: string[] = [`${environment.msalScopeBaseUrl}/App.View`];

export const MSAL_CLIENT_CONFIG = new PublicClientApplication({
	auth: {
		clientId: environment.msalClientId,
		authority: environment.msalAuthority,
		knownAuthorities: [environment.msalAuthority],
		redirectUri: environment.msalAfterLoginUrl,
		postLogoutRedirectUri: environment.msalAfterLogoutUrl,
	},
	cache: {
		cacheLocation: BrowserCacheLocation.SessionStorage,
		storeAuthStateInCookie: IS_INTERNET_EXPLORER,
	},
	system: {
		loggerOptions: {
			loggerCallback: (level: LogLevel, message: string) => {
				// eslint-disable-next-line
				console.info(level, message);
			},
			piiLoggingEnabled: false,
		},
	},
});

export const MSAL_GUARD_CONFIG = {
	interactionType: InteractionType.Redirect,
} as MsalGuardConfiguration;

export const MSAL_INTERCEPTOR_CONFIG = {
	interactionType: InteractionType.Redirect,
	protectedResourceMap: new Map<string, string[]>([[environment.baseApi, MSAL_AUTH_SCOPES]]),
} as MsalInterceptorConfiguration;
