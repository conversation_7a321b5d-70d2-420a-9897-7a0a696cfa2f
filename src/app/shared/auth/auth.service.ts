import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from '@shared/services/api.service';

@Injectable({
	providedIn: 'root',
})
export class AuthService extends ApiService {
	private readonly tokenKey = 'auth_token';

	constructor(http: HttpClient) {
		super(http);
	}

	login(params?: any): Observable<any> {
		return super.getData<{ data: string }>('user/user/token', params).pipe(
			tap((response) => {
				this.setToken(response.data);
			}),
			catchError((error) => {
				return throwError(() => error);
			})
		);
	}

	getToken(): string | null {
		return localStorage.getItem(this.tokenKey);
	}

	setToken(token: string): void {
		localStorage.setItem(this.tokenKey, token);
	}

	clearToken(): void {
		localStorage.removeItem(this.tokenKey);
	}

	isLoggedIn(): boolean {
		return !!localStorage.getItem(this.tokenKey);
	}

	logout(): void {
		this.clearToken();
	}
}
