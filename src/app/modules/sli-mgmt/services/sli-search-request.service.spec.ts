import { TestBed } from '@angular/core/testing';
import { SliSearchRequestService } from './sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { SLI_LIST } from '../ref-data/sli-list.data';
import { SliListObject } from '../models/sli-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { SliSearchPayload } from '../models/sli-search-payload.model';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';

describe('SliSearchRequestService', () => {
	let service: SliSearchRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;
	const baseUrl = 'http://10.10.11.10/prod-api';

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [SliSearchRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(SliSearchRequestService);
	});

	describe('getOptions', () => {
		it('should retrieve all airports when id is "airport"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(AIRPORTS.length);
					expect(response[0].code).toBe(AIRPORTS[0].code);
					expect(response[0].name).toBe(AIRPORTS[0].name);
					done();
				},
				error: done.fail,
			});
		});

		it('should filter airports by keyword', (done: DoneFn) => {
			// Arrange
			const keyword = 'LAX';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					expect(response[0].name).toBe('Los Angeles International Airport');
					done();
				},
				error: done.fail,
			});
		});

		it('should retrieve shipper organizations when id is "shipper"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'shipper';

			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
			];

			const mockApiResponse = {
				data: mockOrganizations,
				code: 200,
				msg: 'Success',
			};

			httpClientSpy.get.and.returnValue(of(mockApiResponse));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Delta Air Lines');
					expect(response[0].name).toBe('Delta Air Lines');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(baseUrl + '/sli/listSliOrgName', {
				params: jasmine.any(Object),
			});
		});
	});

	describe('getSliList', () => {
		it('should retrieve all SLI list items', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: SLI_LIST,
				total: SLI_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(SLI_LIST.length);
					expect(response.total).toBe(SLI_LIST.length);
					expect(response.rows[0].waybillNumber).toBe(SLI_LIST[0].waybillNumber);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(baseUrl + '/sli', { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {
				sliCodeList: ['S000920'],
				departureLocationList: ['LAX'],
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: [SLI_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].waybillNumber).toBe('S000920');
					expect(response.rows[0].departureLocation).toBe('LAX');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(baseUrl + '/sli', { params: jasmine.any(Object) });
		});
	});
});
