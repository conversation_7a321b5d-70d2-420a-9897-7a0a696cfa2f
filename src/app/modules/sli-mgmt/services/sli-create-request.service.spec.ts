import { TestBed } from '@angular/core/testing';
import { SliCreateRequestService } from './sli-create-request.service';
import { map, of } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { COUNTRIES } from '../ref-data/countries.data';
import { CURRENCIES } from '../ref-data/currencies.data';
import { INCOTERMS } from '../ref-data/incoterms.data';
import { Country } from '../models/country.model';
import { Province } from '../models/province.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

// Mock data
const mockProvinces: Province[] = [
	{ code: 'P1', name: 'Province A', cities: [] },
	{ code: 'P2', name: 'Province B', cities: [] },
	{ code: 'P3', name: 'Province C', cities: [] },
];

const mockCountry: Country = {
	name: 'Test Country',
	code: 'TC',
	provinces: [...mockProvinces],
};

export class SliCreateRequestServiceMock {
	getCountries() {
		return of([]);
	}

	getProvinces() {
		return of([]);
	}

	getCities() {
		return of([]);
	}

	getAirports() {
		return of([]);
	}

	getCurrencies() {
		return of([]);
	}

	getIncoterms() {
		return of([]);
	}
}

describe('SliCreateRequestService', () => {
	let service: SliCreateRequestService;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			providers: [provideHttpClient(withInterceptorsFromDi())],
		});
	});

	beforeEach(() => {
		service = TestBed.inject(SliCreateRequestService);
	});

	// #getCountries start
	it('#getCountries should retrieve all countries', () => {
		service.getCountries().subscribe((response: Country[]) => {
			expect(response).toHaveSize(COUNTRIES.length);
		});
	});

	it('#getCountries should return filtered country when code is provided', () => {
		const testCode = 'US';
		service.getCountries(testCode).subscribe((result) => {
			expect(result.length).toBe(1);
			expect(result[0].code).toBe(testCode);
		});
	});

	it('#getCountries should return empty array when non-existent code is provided', () => {
		service.getCountries('XX').subscribe((result) => {
			expect(result.length).toBe(0);
		});
	});

	it('#getCountries should maintain provinces data in the response', () => {
		service.getCountries().subscribe((result) => {
			expect(result[0].provinces).toBeDefined();
			expect(Array.isArray(result[0].provinces)).toBe(true);
		});
	});
	// #getCountries end

	// #getProvinces start
	it('#getProvinces should filter provinces by code when code is provided', () => {
		const result$ = service.getProvinces(mockCountry, 'P2');

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(1);
			expect(provinces[0].code).toBe('P2');
		});
	});

	it('#getProvinces should return empty array when no matching code found', () => {
		const result$ = service.getProvinces(mockCountry, 'INVALID_CODE');

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(0);
		});
	});

	it('#getProvinces should maintain the cities array in the output', () => {
		const testProvince: Province = {
			code: 'TEST',
			name: 'Test Province',
			cities: [{ code: 'C1', name: 'City 1' }],
		};
		const testCountry: Country = {
			name: 'Test Country',
			code: 'TC',
			provinces: [testProvince],
		};

		const result$ = service.getProvinces(testCountry);

		result$.subscribe((provinces: Province[]) => {
			expect(provinces[0].cities).toEqual(testProvince.cities);
		});
	});

	it('#getProvinces should handle empty provinces array', () => {
		const emptyCountry: Country = {
			name: 'Empty Country',
			code: 'EC',
			provinces: [],
		};

		const result$ = service.getProvinces(emptyCountry);

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(0);
		});
	});
	// #getProvinces start

	// #getCities start
	it('#getCities should handle empty cities array', () => {
		const mockProvince: Province = {
			name: 'Test Province',
			code: 'TP',
			cities: [],
		};

		of(mockProvince.cities)
			.pipe(
				map((cities: CodeName[]) => {
					return cities.map((city: CodeName) => {
						return { code: city.code, name: city.name };
					});
				})
			)
			.subscribe((result) => {
				expect(result).toEqual([]);
			});
	});
	// #getCities end

	it('#getAirports should retrieve all airports', () => {
		service.getAirports().subscribe((response: CodeName[]) => {
			expect(response).toHaveSize(AIRPORTS.length);
		});
	});

	it('#getCurrencies should retrieve all currencies', () => {
		service.getCurrencies().subscribe((response: string[]) => {
			expect(response).toHaveSize(CURRENCIES.length);
		});
	});

	it('#getIncoterms should retrieve all incoterms', () => {
		service.getIncoterms().subscribe((response: CodeName[]) => {
			expect(response).toHaveSize(INCOTERMS.length);
		});
	});
});
