import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { PieceItem } from '../../models/piece/piece-item.model';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';
const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';

@Component({
	selector: 'orll-sli-piece-item',
	templateUrl: './sli-piece-item.component.html',
	styleUrl: './sli-piece-item.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		MatRadioModule,
		MatInputModule,
		MatListModule,
		MatDividerModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		FormsModule,
		CommonModule,
		TranslateModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SliPieceItemComponent extends DestroyRefComponent {
	isContained = 'no';
	pieceItemList: PieceItem[] = [];

	sliPieceItemForm: FormGroup = new FormGroup({
		description: new FormControl<string>(''),
		weight: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		quantity: new FormControl<string>('', [Validators.pattern(REGX_POSITIVE_NUMBER)]),
	});

	constructor() {
		super();
	}

	addPieceItem(): void {
		if (this.sliPieceItemForm.invalid) {
			return;
		}

		this.pieceItemList.push({
			product: {
				description: this.sliPieceItemForm.value.description ?? '',
			},
			weight: Number(this.sliPieceItemForm.value.weight ?? ''),
			itemQuantity: Number(this.sliPieceItemForm.value.quantity ?? ''),
		});

		this.sliPieceItemForm.reset();
	}

	deletePieceItem(index: number): void {
		this.pieceItemList.splice(index, 1);
	}
}
