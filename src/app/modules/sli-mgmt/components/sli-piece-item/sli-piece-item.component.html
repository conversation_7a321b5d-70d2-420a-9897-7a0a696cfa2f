<div class="orll-piece-add-item">
	<div class="orll-piece-add-item__header">
		<div class="orll-piece-add-item__title">{{'sli.piece.item.title' | translate}}</div>
		<mat-radio-group [(ngModel)]="isContained" class="orll-piece-add-item__radio-group"
			aria-label="{{'sli.piece.item.title' | translate}}">
			<mat-radio-button value="yes">{{'sli.piece.contained.yes' | translate}}</mat-radio-button>
			<mat-radio-button value="no">{{'sli.piece.contained.no' | translate}}</mat-radio-button>
		</mat-radio-group>
	</div>

	<div class="orll-piece-add-item__body">
		@if (isContained === 'no') {
			<mat-expansion-panel class="orll-piece-add-item__panel" expanded>
				<mat-expansion-panel-header>
					<mat-panel-description>
						<div class="orll-piece-add-item__total-items">
							<span>{{'sli.piece.item.total' | translate}}: {{pieceItemList.length}}</span>
						</div>
					</mat-panel-description>
				</mat-expansion-panel-header>

				<form [formGroup]="sliPieceItemForm">
					<div class="row">
						<mat-form-field appearance="outline" class="col-4">
							<mat-label>{{'sli.piece.item.description' | translate}}</mat-label>
							<input matInput formControlName="description" placeholder="{{'sli.piece.item.description' | translate}}">
						</mat-form-field>

						<mat-form-field appearance="outline" class="col-3">
							<mat-label>{{'sli.piece.item.weight' | translate}}</mat-label>
							<input matInput formControlName="weight" placeholder="{{'sli.piece.item.weight' | translate}}">
							<span matSuffix class="unit">KG</span>
							@if (sliPieceItemForm.get('weight')?.hasError('pattern')) {
								<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
							}
						</mat-form-field>

						<mat-form-field appearance="outline" class="col-3">
							<mat-label>{{'sli.piece.item.quantity' | translate}}</mat-label>
							<input matInput formControlName="quantity" placeholder="{{'sli.piece.item.quantity' | translate}}">
							@if (sliPieceItemForm.get('quantity')?.hasError('pattern')) {
								<mat-error>{{'sli.mgmt.pieceList.pattern.positiveNumber' | translate}}</mat-error>
							}
						</mat-form-field>

						<button mat-raised-button (click)="addPieceItem()" class="col-1 orll-piece-add-item__add-button">
							<mat-icon>add</mat-icon>
							{{'sli.piece.item.add' | translate}}
						</button>
					</div>
				</form>

				<mat-list>
					@for (pieceItem of pieceItemList; track pieceItem; let pieceItemIndex = $index) {
						<mat-list-item>
							<div class="piece-item-content">
								<div class="item-info">
									<div class="item-description col-6">{{pieceItem.product.description}}</div>
									<div class="item-weight col-3">{{'sli.piece.item.weight' | translate}}: <span class="value">{{pieceItem.weight}}KG</span></div>
									<div class="item-quantity col-3">{{'sli.piece.item.quantity' | translate}}: <span class="value">{{pieceItem.itemQuantity}}</span></div>
								</div>
								<button mat-icon-button color="primary" (click)="deletePieceItem(pieceItemIndex)" class="orll-piece-add-item__delete-button">
									<mat-icon>delete</mat-icon>
								</button>
							</div>
						</mat-list-item>
						@if (!$last) {
						<mat-divider></mat-divider>
						}
					}
				</mat-list>
			</mat-expansion-panel>
		}
	</div>
</div>
