.orll-sli-search-form {
	&__title {
		color: var(--iata-blue-primary);
		font-size: 40px;
		margin-bottom: 40px;
	}

	&__footer {
		display: flex;
		justify-content: flex-end;
		padding-top: 20px;
	}

	&__reset-button {
		margin-right: 20px;
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
	}

	.width-100 {
		width: 100%;
	}

	// Override Material styles for form fields
	::ng-deep {
		// Basic form field styling
		.mat-mdc-text-field-wrapper {
			background-color: var(--iata-white);
			border: 1px solid var(--iata-grey-200);
		}

		// Icon prefix styling
		.mat-mdc-form-field-icon-prefix {
			color: var(--iata-blue-primary);
		}
	}
}
