import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { Piece } from '../../models/piece/piece.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { AddPieceDialogComponent } from '../add-piece-dialog/add-piece-dialog.component';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';

@Component({
	selector: 'orll-sli-piece-table',
	templateUrl: './sli-piece-table.component.html',
	styleUrl: './sli-piece-table.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatMenuModule,
		MatIconModule,
		MatCheckboxModule,
		MatPaginatorModule,
		TranslateModule,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class SliPieceTableComponent extends DestroyRefComponent implements OnChanges {
	@Input() records: Piece[] = [];
	@Input() totalRecords = 0;

	@Output() pagination: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();
	@Output() saveRequest = new EventEmitter<void>();

	constructor(private readonly dialog: MatDialog) {
		super();
	}

	readonly displayedColumns: string[] = [
		'select',
		'productDescription',
		'packagingType',
		'grossWeight',
		'dimensions',
		'pieceQuantity',
		'slac',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<Piece>(this.records || []);
	selection = new SelectionModel<Piece>(true, []);

	@ViewChild(MatSort) sort!: MatSort;

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
			this.dataSource.sort = this.sort;
			this.selection.clear();
		}
	}

	isAllSelected() {
		const numSelected = this.selection.selected.length;
		const numRows = this.dataSource.data.length;
		return numRows > 0 ? numSelected === numRows : false;
	}

	toggleAllRows() {
		if (this.isAllSelected()) {
			this.selection.clear();
			return;
		}
		this.selection.select(...this.dataSource.data);
	}

	trackByUpid(record: Piece): string {
		return record.upid;
	}

	addPiece(): void {
		const dialogRef = this.dialog.open(AddPieceDialogComponent, {
			width: '400px',
			data: {},
		});

		dialogRef.afterClosed().subscribe((result) => {
			this.saveRequest.emit(result);
		});
	}

	delPiece(): void {
		// TO DO: Implement delete piece functionality
	}
}
