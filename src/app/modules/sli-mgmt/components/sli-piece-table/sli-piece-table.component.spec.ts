import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceTableComponent } from './sli-piece-table.component';
import { Piece } from '../../models/piece/piece.model';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { TranslateModule } from '@ngx-translate/core';
import { PageEvent } from '@angular/material/paginator';
import { SimpleChange } from '@angular/core';

describe('SliPieceTableComponent', () => {
	let component: SliPieceTableComponent;
	let fixture: ComponentFixture<SliPieceTableComponent>;
	let mockPieces: Piece[];
	let selectedItems: Piece[];

	beforeEach(async () => {
		selectedItems = [];
		const mockSelection = {
			clear: jasmine.createSpy('clear').and.callFake(() => {
				selectedItems.length = 0;
				return mockSelection;
			}),
			select: jasmine.createSpy('select').and.callFake((...pieces: Piece[]) => {
				pieces.forEach((piece) => {
					if (!selectedItems.includes(piece)) {
						selectedItems.push(piece);
					}
				});
				return mockSelection;
			}),
			get selected() {
				return selectedItems;
			},
		} as any as SelectionModel<Piece>;

		mockPieces = [
			{ upid: '1', product: { description: 'Piece 1', hsCommodityDescription: '' } } as Piece,
			{ upid: '2', product: { description: 'Piece 2', hsCommodityDescription: '' } } as Piece,
		];

		await TestBed.configureTestingModule({
			imports: [SliPieceTableComponent, TranslateModule.forRoot()],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceTableComponent);
		component = fixture.componentInstance;

		// Mock dependencies
		component.selection = mockSelection;
		component.dataSource = new MatTableDataSource<Piece>(mockPieces);
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnChanges', () => {
		it('should update dataSource and clear selection when records change', () => {
			// Set up the component with initial empty data
			component.dataSource = new MatTableDataSource<Piece>([]);

			// Create a changes object with the records property
			const changes = {
				records: new SimpleChange(null, mockPieces, true),
			};

			// Set the records input property
			component.records = mockPieces;

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data contains the mock pieces
			expect(component.dataSource.data.length).toEqual(2);
			expect(component.dataSource.data[0]).toEqual(mockPieces[0]);
			expect(component.dataSource.data[1]).toEqual(mockPieces[1]);

			// Verify that selection was cleared
			expect(component.selection.clear).toHaveBeenCalled();
		});
	});

	describe('isAllSelected', () => {
		it('should return false when no rows are selected', () => {
			selectedItems.length = 0;
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when some rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return true when all rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(...mockPieces);
			expect(component.isAllSelected()).toBe(true);
		});

		it('should return false when dataSource is empty', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<Piece>([]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when dataSource is empty but selection is not', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<Piece>([]);
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when more items are selected than exist in dataSource', () => {
			selectedItems.length = 0;
			const extraPiece = { upid: '3', product: { description: 'Piece 3', hsCommodityDescription: '' } } as Piece;
			component.selection.select(...mockPieces, extraPiece);
			expect(component.isAllSelected()).toBe(false);
		});
	});

	describe('toggleAllRows', () => {
		it('should clear selection when all rows are selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			component.selection.select(...mockPieces);
			const clearSpy = component.selection.clear;

			component.toggleAllRows();

			expect(clearSpy).toHaveBeenCalled();
		});

		it('should select all when none selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			const selectSpy = component.selection.select;

			component.toggleAllRows();

			expect(selectSpy).toHaveBeenCalled();
			expect(selectedItems.length).toBe(mockPieces.length);
		});
	});

	describe('trackByUpid', () => {
		it('should return the upid of the given piece', () => {
			const mockPiece: Piece = {
				upid: 'TEST123',
				type: '',
				product: {} as any,
				packagingType: '' as any,
				packagedidentifier: '',
				nvdForCustoms: false,
				nvdForCarriage: false,
				grossWeight: 0,
				dimensions: {} as any,
				pieceQuantity: 0,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
			};

			expect(component.trackByUpid(mockPiece)).toBe('TEST123');
		});
	});

	describe('pagination', () => {
		it('should emit pagination event when page changes', () => {
			spyOn(component.pagination, 'emit');
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 10,
				length: 100,
			};

			component.pagination.emit(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
		});
	});
});
