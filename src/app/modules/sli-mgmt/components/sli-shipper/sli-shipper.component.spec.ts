import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { SliShipperComponent } from './sli-shipper.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of } from 'rxjs';
import { Country } from '../../models/country.model';
import { Province } from '../../models/province.model';
import { CodeName } from '@shared/models/code-name.model';
import { ShipmentParty } from '../../models/shipment-party.model';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { OrgInfo } from '@shared/models/org-info.model';

describe('SliShipperComponent', () => {
	let component: SliShipperComponent;
	let fixture: ComponentFixture<SliShipperComponent>;
	let sliCreateRequestServiceMock: jasmine.SpyObj<SliCreateRequestService>;

	// Mock data
	const mockCountries: Country[] = [
		{ code: 'US', name: 'United States', provinces: [] },
		{ code: 'CA', name: 'Canada', provinces: [] },
	];

	const mockProvinces: Province[] = [
		{ code: 'NY', name: 'New York', cities: [] },
		{ code: 'CA', name: 'California', cities: [] },
	];

	const mockCities: CodeName[] = [
		{ code: 'NYC', name: 'New York City' },
		{ code: 'BUF', name: 'Buffalo' },
	];

	// Helper function to fill form with valid data
	function fillFormWithValidData(): void {
		component.sliShipperForm.patchValue({
			companyName: 'Test Company',
			contactName: 'John Doe',
			countryCode: 'US',
			regionCode: 'NY',
			cityCode: 'NYC',
			locationName: '123 Test St',
			phoneNumber: '1234567890',
			emailAddress: '<EMAIL>',
			postalCode: '12345',
		});
	}

	beforeEach(async () => {
		// Create spy for SliCreateRequestService with stricter typing
		sliCreateRequestServiceMock = jasmine.createSpyObj<SliCreateRequestService>('SliCreateRequestService', [
			'getCountries',
			'getProvinces',
			'getCities',
		]);

		// Configure mock methods
		sliCreateRequestServiceMock.getCountries.and.returnValue(of(mockCountries));
		sliCreateRequestServiceMock.getProvinces.and.returnValue(of(mockProvinces));
		sliCreateRequestServiceMock.getCities.and.returnValue(of(mockCities));

		await TestBed.configureTestingModule({
			imports: [
				SliShipperComponent,
				TranslateModule.forRoot(),
				NoopAnimationsModule, // Required for Material components
			],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceMock },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliShipperComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with empty form values', () => {
			const form = component.sliShipperForm;
			Object.keys(form.controls).forEach((controlName) => {
				expect(form.get(controlName)?.value).toBe('');
			});
		});

		it('should fetch countries on init', () => {
			expect(sliCreateRequestServiceMock.getCountries).toHaveBeenCalledTimes(1);
			expect(component.countries).toEqual(mockCountries);
			expect(component.filteredCountries).toEqual(mockCountries);
		});
	});

	describe('Autocomplete Functionality', () => {
		it('should fetch provinces when country changes', fakeAsync(() => {
			const mockEvent = { option: { value: 'US' } } as MatAutocompleteSelectedEvent;
			component.countryValueChange(mockEvent);
			tick();

			expect(sliCreateRequestServiceMock.getProvinces).toHaveBeenCalledWith(
				jasmine.objectContaining({ code: 'US', name: 'United States' })
			);
			expect(component.provinces).toEqual(mockProvinces);
			expect(component.filteredProvinces).toEqual(mockProvinces);
		}));

		it('should fetch cities when region changes', fakeAsync(() => {
			// First set provinces
			component.provinces = mockProvinces;
			fixture.detectChanges();

			const mockEvent = { option: { value: 'NY' } } as MatAutocompleteSelectedEvent;
			component.regionValueChange(mockEvent);
			tick();

			expect(sliCreateRequestServiceMock.getCities).toHaveBeenCalledWith(jasmine.objectContaining({ code: 'NY', name: 'New York' }));
			expect(component.cities).toEqual(mockCities);
			expect(component.filteredCities).toEqual(mockCities);
		}));

		it('should correctly display country name', () => {
			component.countries = mockCountries;
			expect(component.displayCountryName('US')).toBe('United States');
			expect(component.displayCountryName('INVALID')).toBe('');
		});

		it('should correctly display province name', () => {
			component.provinces = mockProvinces;
			expect(component.displayProvinceName('NY')).toBe('New York');
			expect(component.displayProvinceName('INVALID')).toBe('');
		});

		it('should correctly display city name', () => {
			component.cities = mockCities;
			expect(component.displayCityName('NYC')).toBe('New York City');
			expect(component.displayCityName('INVALID')).toBe('');
		});
	});

	describe('Form Validation', () => {
		it('should validate required fields', () => {
			const form = component.sliShipperForm;
			expect(form.valid).toBeFalsy();

			fillFormWithValidData();
			expect(form.valid).toBeTruthy();
		});

		it('should validate email format', () => {
			const emailControl = component.sliShipperForm.get('emailAddress');
			emailControl?.setValue('invalid-email');
			expect(emailControl?.valid).toBeFalsy();
			expect(emailControl?.hasError('email')).toBeTruthy();

			emailControl?.setValue('<EMAIL>');
			expect(emailControl?.valid).toBeTruthy();
		});
	});

	describe('Form Data Retrieval', () => {
		it('should return null from getFormData when form is invalid', () => {
			// Form is initially invalid
			expect(component.getFormData()).toBeNull();
		});

		it('should return ShipmentParty object from getFormData when form is valid', () => {
			fillFormWithValidData();

			const result = component.getFormData();
			const expected: ShipmentParty = {
				companyName: 'Test Company',
				contactName: 'John Doe',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				postalCode: '12345',
				locationName: '123 Test St',
				phoneNumber: '1234567890',
				emailAddress: '<EMAIL>',
				companyType: '',
			};

			expect(result).toEqual(expected);
		});

		it('should return data even when form is invalid if ignore flag is true', () => {
			// Set only some fields, leaving required fields empty
			component.sliShipperForm.patchValue({
				companyName: 'Test Company',
				emailAddress: '<EMAIL>',
			});

			// Should return null without ignore flag
			expect(component.getFormData()).toBeNull();

			// Should return data with ignore flag
			const result = component.getFormData(true);
			expect(result).not.toBeNull();
			expect(result?.companyName).toBe('Test Company');
		});
	});

	describe('Filtering Functionality', () => {
		it('should filter countries based on search input', fakeAsync(() => {
			component.countries = mockCountries;
			component.filteredCountries = mockCountries;

			// Simulate typing in country autocomplete
			component.sliShipperForm.get('countryCode')?.setValue('United');
			tick();

			expect(component.filteredCountries.length).toBe(1);
			expect(component.filteredCountries[0].name).toBe('United States');
		}));

		it('should filter provinces based on search input', fakeAsync(() => {
			component.provinces = mockProvinces;
			component.filteredProvinces = mockProvinces;

			// First setup country to enable province filtering
			component.countryValueChange({ option: { value: 'US' } } as MatAutocompleteSelectedEvent);
			tick();

			// Simulate typing in province autocomplete
			component.sliShipperForm.get('regionCode')?.setValue('New');
			tick();

			expect(component.filteredProvinces.length).toBe(1);
			expect(component.filteredProvinces[0].name).toBe('New York');
		}));

		it('should filter cities based on search input', fakeAsync(() => {
			component.cities = mockCities;
			component.filteredCities = mockCities;

			// First setup country and province
			component.provinces = mockProvinces;
			component.regionValueChange({ option: { value: 'NY' } } as MatAutocompleteSelectedEvent);
			tick();

			// Simulate typing in city autocomplete
			component.sliShipperForm.get('cityCode')?.setValue('New York');
			tick();

			expect(component.filteredCities.length).toBe(1);
			expect(component.filteredCities[0].name).toBe('New York City');
		}));

		it('should return empty array when no matches found in filtering', fakeAsync(() => {
			component.countries = mockCountries;
			component.filteredCountries = mockCountries;

			component.sliShipperForm.get('countryCode')?.setValue('NonExistent');
			tick();

			expect(component.filteredCountries.length).toBe(0);
		}));
	});

	describe('Shipper Info Input Handling', () => {
		it('should fill form when shipperInfo is provided', () => {
			const mockShipperInfo: OrgInfo = {
				id: '1111111',
				companyName: 'Test Shipper Company',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '456 Shipper St',
				partyRole: 'SHIPPER',
				persons: [
					{
						contactRole: 'CUSTOMER_CONTACT',
						contactName: 'Jane Smith',
						phoneNumber: '9876543210',
						emailAddress: '<EMAIL>',
						jobTitle: '',
						employeeId: '',
					},
				],
			};

			component.shipperInfo = mockShipperInfo;
			component.countries = mockCountries;
			component.provinces = mockProvinces;
			component.ngOnChanges({
				shipperInfo: { currentValue: mockShipperInfo, previousValue: null, firstChange: true, isFirstChange: () => true },
			});

			expect(component.sliShipperForm.get('companyName')?.value).toBe('Test Shipper Company');
			expect(component.sliShipperForm.get('contactName')?.value).toBe('Jane Smith');
			expect(component.sliShipperForm.get('countryCode')?.value).toBe('US');
			expect(component.sliShipperForm.get('regionCode')?.value).toBe('NY');
			expect(component.sliShipperForm.get('cityCode')?.value).toBe('NYC');
			expect(component.sliShipperForm.get('postalCode')?.value).toBe('10001');
			expect(component.sliShipperForm.get('locationName')?.value).toBe('456 Shipper St');
			expect(component.sliShipperForm.get('phoneNumber')?.value).toBe('9876543210');
			expect(component.sliShipperForm.get('emailAddress')?.value).toBe('<EMAIL>');
		});

		it('should handle shipperInfo with no customer contact person', () => {
			const mockShipperInfo: OrgInfo = {
				id: '1111111',
				companyName: 'Test Company',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '456 Test St',
				partyRole: 'SHIPPER',
				persons: [],
			};

			component.shipperInfo = mockShipperInfo;
			component.countries = mockCountries;
			component.provinces = mockProvinces;
			component.ngOnChanges({
				shipperInfo: { currentValue: mockShipperInfo, previousValue: null, firstChange: true, isFirstChange: () => true },
			});

			expect(component.sliShipperForm.get('contactName')?.value).toBe('');
			expect(component.sliShipperForm.get('phoneNumber')?.value).toBe('');
			expect(component.sliShipperForm.get('emailAddress')?.value).toBe('');
		});

		it('should not fill form when shipperInfo is null', () => {
			component.shipperInfo = null;
			component.ngOnChanges({
				shipperInfo: { currentValue: null, previousValue: null, firstChange: true, isFirstChange: () => true },
			});

			// Form should remain empty
			expect(component.sliShipperForm.get('companyName')?.value).toBe('');
			expect(component.sliShipperForm.get('contactName')?.value).toBe('');
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle empty search strings in filtering', fakeAsync(() => {
			component.countries = mockCountries;
			component.filteredCountries = [];

			component.sliShipperForm.get('countryCode')?.setValue('');
			tick();

			expect(component.filteredCountries).toEqual(mockCountries);
		}));

		it('should handle null/undefined values in display functions', () => {
			component.countries = mockCountries;
			component.provinces = mockProvinces;
			component.cities = mockCities;

			expect(component.displayCountryName('')).toBe('');
			expect(component.displayProvinceName('')).toBe('');
			expect(component.displayCityName('')).toBe('');
		});

		it('should handle countryValueChange with undefined event', () => {
			expect(() => component.countryValueChange()).not.toThrow();
			expect(sliCreateRequestServiceMock.getProvinces).toHaveBeenCalled();
		});

		it('should handle regionValueChange with undefined event', () => {
			component.provinces = mockProvinces;
			expect(() => component.regionValueChange()).not.toThrow();
			expect(sliCreateRequestServiceMock.getCities).toHaveBeenCalled();
		});

		it('should validate phone number as required field', () => {
			const phoneControl = component.sliShipperForm.get('phoneNumber');
			expect(phoneControl?.hasError('required')).toBeTruthy();

			phoneControl?.setValue('1234567890');
			expect(phoneControl?.hasError('required')).toBeFalsy();
		});

		it('should validate postal code as optional field', () => {
			const postalCodeControl = component.sliShipperForm.get('postalCode');
			expect(postalCodeControl?.hasError('required')).toBeFalsy();
		});
	});
});
