<div class="orll-sli-routing-form">
	<form [formGroup]="sliRoutingForm">
		<div class="row">
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100 orll-sli-routing-form__departure">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{'sli.mgmt.routing.departureLocation' | translate}}</mat-label>
					<input type="text" matInput
						formControlName="departureLocation"
						[matAutocomplete]="autoDeparture">
					<mat-autocomplete #autoDeparture="matAutocomplete" [displayWith]="displayAirportName">
						@for (airport of filteredDepartureAirports; track airport) {
							<mat-option [value]="airport.code">{{airport.name}}</mat-option>
						}
					</mat-autocomplete>
					@if (sliRoutingForm.get('departureLocation')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.routing.departureLocation.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>

			<div class="col-9">
				<mat-form-field appearance="outline" class="width-100">
					<mat-label>{{'sli.mgmt.routing.shippingInfo' | translate}}</mat-label>
					<textarea matInput formControlName="shippingInfo" rows="4"></textarea>
				</mat-form-field>
			</div>

			<div class="col-3 margin-t-78">
				<mat-form-field appearance="outline" class="width-100 orll-sli-routing-form__arrival">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{'sli.mgmt.routing.arrivalLocation' | translate}}</mat-label>
					<input type="text" matInput
						formControlName="arrivalLocation"
						[matAutocomplete]="autoArrival">
					<mat-autocomplete #autoArrival="matAutocomplete" [displayWith]="displayAirportName">
						@for (airport of filteredArrivalAirports; track airport) {
							<mat-option [value]="airport.code">{{airport.name}}</mat-option>
						}
					</mat-autocomplete>
					@if (sliRoutingForm.get('arrivalLocation')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.routing.arrivalLocation.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
		</div>
	</form>
</div>
