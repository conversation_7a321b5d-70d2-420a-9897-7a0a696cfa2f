import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SliPieceFormComponent } from './sli-piece-form.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';

describe('SliPieceFormComponent', () => {
	let component: SliPieceFormComponent;
	let fixture: ComponentFixture<SliPieceFormComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [SliPieceFormComponent, TranslateModule.forRoot()],
			providers: [provideHttpClient(withInterceptorsFromDi())],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceFormComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
