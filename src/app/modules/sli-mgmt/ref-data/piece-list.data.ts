import { Piece } from '../models/piece/piece.model';

export const PIECE_LIST: Piece[] = [
	{
		type: 'Piece',
		product: {
			description: 'GPU,CPU',
			hsCommodityDescription: '',
		},
		packagingType: {
			typeCode: 'PT001',
			description: 'Box, plastic',
		},
		packagedidentifier: '',
		nvdForCustoms: false,
		nvdForCarriage: false,
		grossWeight: 5,
		upid: '',
		dimensions: {
			length: 110,
			width: 50,
			height: 30,
		},
		shippingMarks: '',
		textualHandlingInstructions: '',
		pieceQuantity: 1,
		slac: 0,
		containedPieces: [],
		containedItems: [
			{
				product: {
					description: '',
					hsCommodityDescription: '',
				},
				itemQuantity: 0,
				weight: 0,
			},
		],
	},
	{
		type: 'Piece',
		product: {
			description: 'Computer Accessories',
			hsCommodityDescription: '',
		},
		packagingType: {
			typeCode: 'PT002',
			description: 'Box, fibreboard',
		},
		packagedidentifier: '',
		nvdForCustoms: false,
		nvdForCarriage: false,
		grossWeight: 10,
		upid: '',
		dimensions: {
			length: 220,
			width: 100,
			height: 50,
		},
		shippingMarks: '',
		textualHandlingInstructions: '',
		pieceQuantity: 2,
		slac: 0,
		containedPieces: [],
		containedItems: [
			{
				product: {
					description: '',
					hsCommodityDescription: '',
				},
				itemQuantity: 0,
				weight: 0,
			},
		],
	},
];
