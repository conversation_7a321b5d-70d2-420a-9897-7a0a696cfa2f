import { Product } from './product.model';
import { PackagingType } from './packaging-type.model';
import { Dimensions } from './dimensions.model';
import { PieceItem } from './piece-item.model';

export interface Piece {
	upid: string;
	type: string;
	product: Product;
	packagingType: PackagingType;
	packagedidentifier: string;
	nvdForCustoms: boolean;
	nvdForCarriage: boolean;
	grossWeight: number;
	dimensions: Dimensions;
	pieceQuantity: number;
	slac: number;
	shippingMarks: string;
	textualHandlingInstructions: string;
	containedPieces: Piece[];
	containedItems: PieceItem[];
}
