import { ChangeDetectionStrategy, ChangeDetector<PERSON><PERSON>, Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router } from '@angular/router';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ShipmentParty } from '../../models/shipment-party.model';
import { SliShipperComponent } from '../../components/sli-shipper/sli-shipper.component';
import { SliConsigneeComponent } from '../../components/sli-consignee/sli-consignee.component';
import { SliRoutingComponent } from '../../components/sli-routing/sli-routing.component';
import { S<PERSON><PERSON><PERSON><PERSON><PERSON>istComponent } from '../../components/sli-piece-list/sli-piece-list.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SliCreatePayload } from '../../models/sli-create-payload.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatDialog } from '@angular/material/dialog';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { map, Observable } from 'rxjs';
import { CanComponentDeactivate } from '@shared/services/can-deactivate.guard';
import { Person } from '@shared/models/person.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { environment } from '@environments/environment';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { NotificationService } from '@shared/services/notification.service';

@Component({
	selector: 'orll-sli-create-page',
	templateUrl: './sli-create-page.component.html',
	styleUrls: ['./sli-create-page.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		TranslateModule,
		SliShipperComponent,
		SliConsigneeComponent,
		SliRoutingComponent,
		SliPieceListComponent,
	],
})
export default class SliCreatePageComponent extends DestroyRefComponent implements OnInit, CanComponentDeactivate {
	@ViewChildren(SliShipperComponent) sliShipper!: QueryList<SliShipperComponent>;
	@ViewChildren('sliConsignee') sliConsignee!: QueryList<SliConsigneeComponent>;
	@ViewChildren(SliRoutingComponent) sliRouting!: QueryList<SliRoutingComponent>;
	@ViewChildren(SliPieceListComponent) sliPieceList!: QueryList<SliPieceListComponent>;

	sliNumber: string | null = '';
	shipperInfo: OrgInfo | null = null;
	alsoNotifies: ShipmentParty[] = [];
	dataLoading = false;
	isSaved = false;
	isConfirmed = false;
	isPiece = false;
	pieceType = '';

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute,
		private readonly cdr: ChangeDetectorRef,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly notificationService: NotificationService,
		private readonly translate: TranslateService,
		private readonly dialog: MatDialog
	) {
		super();
	}

	ngOnInit(): void {
		this.sliNumber = this.route.snapshot.paramMap.get('sliNumber');
		this.fillShipperInfo();
	}

	fillShipperInfo(): void {
		this.orgMgmtRequestService
			.getOrgInfo(environment.orgId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.shipperInfo = res;
					this.cdr.markForCheck();
				},
				error: (err) => {
					this.notificationService.showError(this.translate.instant('sli.dialog.shipper.fail') + `: ${err.message}`);
				},
			});
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			postalCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					return {
						...item,
						companyName: result.companyName,
						contactName:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						postalCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
						emailAddress:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
						companyType: result.partyRole,
					};
				}
				return item;
			});

			this.cdr.markForCheck();
		});
	}

	onSave(pieceType?: string): void {
		this.sliShipper.forEach((comp) => comp.sliShipperForm?.markAllAsTouched());
		this.sliRouting.forEach((comp) => comp.sliRoutingForm?.markAllAsTouched());
		this.sliPieceList.forEach((comp) => comp.sliPieceListForm?.markAllAsTouched());

		const shipperData = this.sliShipper.first?.getFormData();
		const consigneeData = this.sliConsignee.first?.getFormData();
		const routingData = this.sliRouting.first?.getFormData();
		const pieceListData = this.sliPieceList.first?.getFormData();

		if (!shipperData || !routingData || !pieceListData) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		shipperData.companyType = 'SHP';
		const shipmentPartyList = [shipperData, ...this.alsoNotifies];
		if (consigneeData) shipmentPartyList.push(consigneeData);

		const sliCreatePayload = {
			shipmentParty: shipmentPartyList,
			...routingData,
			...pieceListData,
		} as SliCreatePayload;

		this.dataLoading = true;

		this.sliCreateRequestService
			.createSli(sliCreatePayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();
					this.notificationService.showSuccess(this.translate.instant('sli.dialog.create.success'));
					if (pieceType) {
						this.router.navigate(['piece', pieceType], { relativeTo: this.route });
					} else {
						this.router.navigate(['sli'], { relativeTo: this.route });
					}
				},
				error: (err) => {
					this.dataLoading = false;
					this.notificationService.showError(this.translate.instant('sli.dialog.create.fail') + `: ${err.message}`);
				},
			});
	}

	onCancel(): void {
		if (this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			dialogRef.afterClosed().subscribe((confirmed) => {
				if (confirmed) {
					this.isConfirmed = true;
					this.router.navigate(['/sli'], { relativeTo: this.route });
				}
			});
		} else {
			this.router.navigate(['/sli'], { relativeTo: this.route });
		}
	}

	canDeactivate(): boolean | Observable<boolean> {
		if (this.isSaved) return true;

		if (!this.isConfirmed && this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			return dialogRef.afterClosed().pipe(map((confirmed) => !!confirmed));
		} else {
			return true;
		}
	}

	hasUnsavedChanges(): boolean {
		const shipperData = this.sliShipper.first?.getFormData(true);
		const hasShipperData = shipperData ? this.hasRealData(shipperData) : false;

		const consigneeData = this.sliConsignee.first?.getFormData();
		const hasConsigneeData = consigneeData ? this.hasRealData(consigneeData) : false;

		const routingData = this.sliRouting.first?.getFormData(true);
		const hasRoutingData = routingData ? this.hasRealData(routingData) : false;

		const pieceListData = this.sliPieceList.first?.getFormData(true);
		const hasPieceListData = pieceListData ? this.hasRealData(pieceListData) : false;

		const hasAlsoNotifies = this.alsoNotifies.some((item) => this.hasRealData(item));

		return hasShipperData || hasConsigneeData || hasAlsoNotifies || hasRoutingData || hasPieceListData;
	}

	hasRealData(obj: Record<string, any>): boolean {
		if (!obj) return false;
		return Object.values(obj).some((value) => {
			if (Array.isArray(value)) return value.length > 0;
			if (typeof value === 'object') return this.hasRealData(value);
			return String(value).trim();
		});
	}
}
