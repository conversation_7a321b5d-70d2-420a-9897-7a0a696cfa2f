import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';

@Component({
	selector: 'orll-piece-add-page',
	templateUrl: './piece-add-page.component.html',
	styleUrl: './piece-add-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [SliPieceFormComponent, SliPieceItemComponent],
})
export default class PieceAddPageComponent extends DestroyRefComponent implements OnInit {
	pieceType: string | null = '';

	constructor(private readonly route: ActivatedRoute) {
		super();
	}

	ngOnInit(): void {
		this.pieceType = this.route.snapshot.paramMap.get('pieceType');
	}
}
