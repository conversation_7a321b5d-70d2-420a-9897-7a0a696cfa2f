import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PieceAddPageComponent from './piece-add-page.component';
import { ActivatedRoute, ParamMap } from '@angular/router';
import { of } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';

describe('PieceAddPageComponent', () => {
	let component: PieceAddPageComponent;
	let fixture: ComponentFixture<PieceAddPageComponent>;
	let mockParamMap: jasmine.SpyObj<ParamMap>;

	beforeEach(async () => {
		mockParamMap = jasmine.createSpyObj<ParamMap>('ParamMap', ['get']);

		await TestBed.configureTestingModule({
			imports: [PieceAddPageComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: ActivatedRoute,
					useValue: {
						snapshot: {
							paramMap: mockParamMap,
						},
						parent: {
							snapshot: {
								url: [],
							},
						},
						queryParams: of({}),
					},
				},
				provideHttpClient(withInterceptorsFromDi()),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceAddPageComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
