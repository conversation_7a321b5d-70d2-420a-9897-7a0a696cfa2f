{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"iata-orll-fe": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/iata-orll-fe"}, "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/assets/fonts/iata-font.css", "src/styles/bootstrap-grid.min.css", "src/styles/styles.scss", {"input": "src/styles/material-theme/iata-material-theme.scss", "bundleName": "iata-material-theme"}], "stylePreprocessorOptions": {"includePaths": ["./src/styles"]}, "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "outputHashing": "all"}, "test": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "iata-orll-fe:build:production"}, "development": {"buildTarget": "iata-orll-fe:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "iata-orll-fe:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "karmaConfig": "karma.conf.js", "codeCoverage": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles/bootstrap-grid.min.css", "src/styles/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["./src/styles"]}, "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": false}}