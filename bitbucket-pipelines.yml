# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: atlassian/default-image:2
# image: hashicorp/terraform:1.0.0

definitions:
    services:
        docker:
            memory: 2048
    caches:
        sonar: ~/.sonar/cache  # Caching SonarCloud artifacts will speed up your build

    steps:
        - step: &TagRelease
              name: 'Tag Release'
              script:
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  - export VERSION=$PACKAGE_VERSION-"${BITBUCKET_COMMIT::7}"
                  - echo 'Tag release to' $VERSION
                  - git tag -fa "$VERSION" -m "Tag Release version $VERSION"
                  - git push --tags origin

        - step: &Sonar
              name: 'Perform sonar scan'
              image: node:20-alpine
              caches:
                  - sonar
              size: 2x
              script:
                  - apk add chromium
                  - export CHROME_BIN='/usr/bin/chromium-browser'
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-test
                  - npm run test:ci
                  - pipe: sonarsource/sonarcloud-scan:2.0.0
                    variables:
                        SONAR_TOKEN: ${SONAR_TOKEN}
                        DEBUG: "true"

        - step: &SecurityCheckSnyk
              name: Security check on Snyk
              image: snyk/snyk:node-20
              script:
                  - snyk auth $SNYK_TOKEN
                  - snyk test --severity-threshold=medium --json-file-output=.snyk-test-report.json
              artifacts:
                  download: true
                  paths:
                      - .snyk-test-report.json

        - step: &DeployTest
              deployment: Test
              image: node:20-alpine
              name: 'Deploy to Test'
              size: 2x
              oidc: true
              caches:
                  - node
                  - docker
              script:
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-test
                  - cd dist/$BITBUCKET_REPO_SLUG/browser
                  - pipe: atlassian/aws-s3-deploy:1.1.0
                    variables:
                        AWS_OIDC_ROLE_ARN: $OIDC_ROLE_ARN
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        S3_BUCKET: $S3_BUCKET_DIST
                        LOCAL_PATH: '$(pwd)'
                        DELETE_FLAG: 'true'
                        CACHE_CONTROL: 'max-age=600'
                  - echo "Deploy Frontend Complete"

        - step: &DeployStaging
              deployment: Staging
              image: node:20-alpine
              name: 'Deploy to Staging'
              size: 2x
              oidc: true
              caches:
                  - node
                  - docker
              script:
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-staging
                  - cd dist/$BITBUCKET_REPO_SLUG/browser
                  - pipe: atlassian/aws-s3-deploy:1.1.0
                    variables:
                        AWS_OIDC_ROLE_ARN: $OIDC_ROLE_ARN
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        S3_BUCKET: $S3_BUCKET_DIST
                        LOCAL_PATH: '$(pwd)'
                        DELETE_FLAG: 'true'
                        CACHE_CONTROL: 'max-age=600'
                  - echo "Deploy Frontend Complete"

        - step: &DeployProduction
              deployment: Production
              image: node:20-alpine
              name: 'Deploy to Production'
              size: 2x
              oidc: true
              caches:
                  - node
                  - docker
              trigger: manual
              script:
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-prod
                  - cd dist/$BITBUCKET_REPO_SLUG/browser
                  - pipe: atlassian/aws-s3-deploy:1.1.0
                    variables:
                        AWS_OIDC_ROLE_ARN: $OIDC_ROLE_ARN
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        S3_BUCKET: $S3_BUCKET_DIST
                        LOCAL_PATH: '$(pwd)'
                        DELETE_FLAG: 'true'
                        CACHE_CONTROL: 'max-age=600'
                  - echo "Deploy Frontend Complete"

pipelines:
    pull-requests:
        '**':
            - parallel:
                  - step: *Sonar
                  - step: *SecurityCheckSnyk

    custom:
        tag-release:
            - step: *TagRelease
        deploy-to-test:
            - step: *DeployTest
        deploy-to-staging:
            - step: *DeployStaging
        deploy-to-production:
            - step: *DeployProduction
        security:
            - step: *SecurityCheckSnyk
        sonar:
            - step: *Sonar
    branches:
        develop:
            - parallel:
                  - step: *Sonar
                #   - step: *DeployTest  # Uncomment this line when the Test environment is ready
        staging:
            - parallel:
                  - step: *Sonar
                  - step: *DeployStaging
        main:
            - step: *Sonar
            - step: *TagRelease
            - step: *DeployProduction
